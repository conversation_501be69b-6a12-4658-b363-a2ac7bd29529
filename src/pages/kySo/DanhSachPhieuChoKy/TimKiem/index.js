import React, { useEffect, useState, useRef, useMemo } from "react";
import { BaseSearch, Button } from "components";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { useEnum, useListAll, useQueryAll, useStore, useThietLap } from "hooks";
import { ENUM, TRANG_THAI_KY } from "constants/index";
import { transformQueryString } from "utils";
import { getChuKySo } from "utils/phieu-utils";
import moment from "moment";
import { SVG } from "assets";
import { debounce } from "lodash";
import { Main } from "./styled";
import { checkRole } from "lib-utils/role-utils";
import { ROLES, THIET_LAP_CHUNG } from "constants/index";
import { query } from "redux-store/stores";

const TimKiem = () => {
  const {
    danhSachPhieuChoKy: { searchByParams, updateData },
    loaiDoiTuong: { getListAllLoaiDoiTuong },
    baoCao: { tongHop: baoCaotongHop },
    phieuIn: { kyPhieu },
  } = useDispatch();

  const { data: listAllKhoaTheoTaiKhoan } = useQueryAll(
    query.khoa.queryKhoaTheoTaiKhoan
  );
  const [state, _setState] = useState({
    dsBaoCaoId: [],
    dsLoaiBaoCaoId: [],
    dsKhoaNbId: listAllKhoaTheoTaiKhoan?.map((item) => item.id) || [],
  });
  const [listAllNhanVien] = useListAll("nhanVien", {}, true);

  const [dataMA_LOAI_PHIEU_MAC_DINH_DS_PHIEU_KY] = useThietLap(
    THIET_LAP_CHUNG.MA_LOAI_PHIEU_MAC_DINH_DS_PHIEU_KY
  );
  const [dataLOC_KHOA_THEO_TAI_KHOAN_DANH_SACH_PHIEU_KY] = useThietLap(
    THIET_LAP_CHUNG.LOC_KHOA_THEO_TAI_KHOAN_DANH_SACH_PHIEU_KY
  );
  const [dataTHIET_LAP_LOC_TAI_KHOAN_BAC_SI] = useThietLap(
    THIET_LAP_CHUNG.THIET_LAP_LOC_TAI_KHOAN_BAC_SI
  );
  const refShowDate = useRef(null);
  const refShowDate1 = useRef(null);
  const refShowDate2 = useRef(null);
  const refTimeout = useRef(null);

  const setState = (data) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };
  const { t } = useTranslation();

  const [listTrangThaiKy] = useEnum(ENUM.TRANG_THAI_KY);
  const [dataTrangThaiBenhAn] = useEnum(ENUM.TRANG_THAI_BENH_AN);
  const [listTrangThaiNb] = useEnum(ENUM.TRANG_THAI_NB);

  const listPhieu = useStore("baoCao.listPhieu", []);
  const selectionIdx = useStore("danhSachPhieuChoKy.selectionIdx", []);
  const listData = useStore("danhSachPhieuChoKy.listData", []);
  const listAllLoaiDoiTuong = useStore("loaiDoiTuong.listAllLoaiDoiTuong", []);
  const nhanVienId = useStore("auth.auth.nhanVienId", null);

  const [listAllLoaiPhieu] = useListAll("loaiPhieu", { active: true }, true);
  const { data: listAllKhoa } = useQueryAll(query.khoa.queryAllKhoa);

  const listKhoaMemo = useMemo(() => {
    return dataLOC_KHOA_THEO_TAI_KHOAN_DANH_SACH_PHIEU_KY?.eval()
      ? listAllKhoaTheoTaiKhoan
      : listAllKhoa;
  }, [
    dataLOC_KHOA_THEO_TAI_KHOAN_DANH_SACH_PHIEU_KY,
    listAllKhoaTheoTaiKhoan,
    listAllKhoa,
  ]);

  useEffect(() => {
    getListAllLoaiDoiTuong({ page: "", size: "", active: true });
    const { page, size, dataSortColumn, ...queries } = transformQueryString({
      dsTrangThai: {
        format: (value) => value.split(",").map(Number),
        defaultValue: [
          TRANG_THAI_KY.TRINH_KY,
          TRANG_THAI_KY.DA_KY,
          TRANG_THAI_KY.TU_CHOI_KY,
        ],
      },
      dsTrangThaiBenhAn: {
        format: (value) => value.split(",").map(Number),
        defaultValue: [],
      },
      dsBaoCaoId: {
        format: (value) => value.split(",").map(Number),
      },
      dsKhoaNbId: {
        format: (value) => value.split(",").map(Number),
        defaultValue: dataLOC_KHOA_THEO_TAI_KHOAN_DANH_SACH_PHIEU_KY?.eval()
          ? listAllKhoaTheoTaiKhoan?.map((item) => item.id)
          : null,
      },
      tuThoiGianTrinhKy: {
        type: "dateOptions",
        defaultValue: moment().subtract(6, "days").startOf("day"),
      },
      denThoiGianTrinhKy: {
        type: "dateOptions",
        defaultValue: moment().endOf("day"),
      },
      tuThoiGianKy: {
        type: "dateOptions",
      },
      denThoiGianKy: {
        type: "dateOptions",
      },
      nguoiKyId: {
        format: (value) => Number(value),
      },
      dsTrangThaiNb: {
        format: (value) => value.split(",").map(Number),
      },
      tuThoiGianRaVien: {
        type: "dateOptions",
      },
      denThoiGianRaVien: {
        type: "dateOptions",
      },
    });

    if (refTimeout.current) clearTimeout(refTimeout.current);
    refTimeout.current = setTimeout(() => {
      const dsLoaiBaoCaoId = listAllLoaiPhieu
        .filter((el) =>
          (dataMA_LOAI_PHIEU_MAC_DINH_DS_PHIEU_KY || "")
            .split(",")
            .includes(el.ma)
        )
        .map((el) => el.id);

      if (dsLoaiBaoCaoId?.length) {
        queries.dsLoaiBaoCaoId = dsLoaiBaoCaoId;
      }
      baoCaotongHop({ page: "", size: "", dsLoaiBaoCaoId });

      if (
        dataTHIET_LAP_LOC_TAI_KHOAN_BAC_SI?.eval() &&
        (queries?.dsTrangThai?.length === listTrangThaiKy?.length ||
          !queries?.dsTrangThai?.length)
      ) {
        queries.nguoiKyId = nhanVienId;
      } else {
        delete queries.nguoiKyId;
      }
      setState(queries);
      searchByParams({
        ...queries,
        tuThoiGianTrinhKy:
          queries.tuThoiGianTrinhKy &&
          moment(queries.tuThoiGianTrinhKy).format("YYYY-MM-DD 00:00:00"),
        denThoiGianTrinhKy:
          queries.denThoiGianTrinhKy &&
          moment(queries.denThoiGianTrinhKy).format("YYYY-MM-DD 23:59:59"),
        tuThoiGianKy:
          queries.tuThoiGianKy &&
          moment(queries.tuThoiGianKy).format("YYYY-MM-DD 00:00:00"),
        denThoiGianKy:
          queries.denThoiGianKy &&
          moment(queries.denThoiGianKy).format("YYYY-MM-DD 23:59:59"),
        tatCaTaiKhoan: checkRole([ROLES["HE_THONG"].XEM_TAT_CA_FILE_KY]),
        tuThoiGianRaVien: queries.tuThoiGianRaVien
          ? moment(queries.tuThoiGianRaVien).format("YYYY-MM-DD 00:00:00")
          : null,
        denThoiGianRaVien: queries.denThoiGianRaVien
          ? moment(queries.denThoiGianRaVien).format("YYYY-MM-DD 23:59:59")
          : null,
      });
    }, 500);

    return () => {
      updateData({
        dataSortColumn: {},
        maHoSo: "",
        dataSearch: {},
      });
    };
  }, [
    listAllLoaiPhieu,
    dataMA_LOAI_PHIEU_MAC_DINH_DS_PHIEU_KY,
    dataLOC_KHOA_THEO_TAI_KHOAN_DANH_SACH_PHIEU_KY,
    dataTHIET_LAP_LOC_TAI_KHOAN_BAC_SI,
  ]);

  const onChange = (data) => {
    searchByParams(data);
  };

  const onChangeStatus = (value) => {
    if (
      (dataTHIET_LAP_LOC_TAI_KHOAN_BAC_SI?.eval() &&
        value?.dsTrangThai?.length === listTrangThaiKy?.length) ||
      !value?.dsTrangThai?.length
    ) {
      setState({ nguoiKyId: nhanVienId });
      searchByParams({ ...value, nguoiKyId: nhanVienId });
    } else {
      searchByParams(value);
    }
  };

  const { showKyHangLoat, listKyHangLoat } = useMemo(() => {
    let _data = listData.filter(
      (item) => selectionIdx.includes(item.index) && item.trangThai < 60
    );

    return {
      showKyHangLoat: _data.length > 0,
      listKyHangLoat: _data,
    };
  }, [listData, selectionIdx]);

  const onKyHangLoat = () => {
    Promise.allSettled(
      listKyHangLoat.map((item) => {
        let payload = {
          id: item?.id,
          chuKySo: getChuKySo(item),
        };

        return kyPhieu(payload);
      })
    ).then((values) => {
      if (values.some((x) => x.status == "fulfilled")) {
        updateData({ selectionIdx: [] });
        searchByParams({});
      }
    });
  };

  const listOptionCustom = [
    {
      key: "today",
      text: t("khamBenh.dsBenhNhan.homNay"),
      tuThoiGianVaoVien: moment().startOf("day"),
      denThoiGianVaoVien: moment().endOf("day"),
    },
    {
      key: "yesterday",
      text: t("khamBenh.dsBenhNhan.homQua"),
      tuThoiGianVaoVien: moment().subtract(1, "days").startOf("day"),
      denThoiGianVaoVien: moment().subtract(1, "days").endOf("day"),
    },
    {
      key: "last7DaysBefore",
      text: t("khamBenh.dsBenhNhan.bayNgayTruoc"),
      tuThoiGianVaoVien: moment().subtract(6, "days").startOf("day"),
      denThoiGianVaoVien: moment().endOf("day"),
    },
    {
      key: "last30DaysBefore",
      text: t("khamBenh.dsBenhNhan.baMuoiNgayTruoc"),
      tuThoiGianVaoVien: moment().subtract(29, "days").startOf("day"),
      denThoiGianVaoVien: moment().endOf("day"),
    },
    {
      key: "currentMonth",
      text: t("khamBenh.dsBenhNhan.thangHienTai"),
      tuThoiGianVaoVien: moment().startOf("month").startOf("day"),
      denThoiGianVaoVien: moment().endOf("month").endOf("day"),
    },
    {
      key: "lastMonth",
      text: t("khamBenh.dsBenhNhan.thangTruoc"),
      tuThoiGianVaoVien: moment()
        .subtract(1, "months")
        .startOf("month")
        .startOf("day"),
      denThoiGianVaoVien: moment()
        .subtract(1, "months")
        .endOf("month")
        .endOf("day"),
    },
    {
      key: "lastYear",
      text: t("khamBenh.dsBenhNhan.namTruoc"),
      tuThoiGianVaoVien: moment()
        .subtract(1, "year")
        .startOf("month")
        .startOf("day"),
      denThoiGianVaoVien: moment()
        .subtract(1, "year")
        .endOf("year")
        .endOf("day"),
    },
    {
      key: "setting",
      text: t("khamBenh.dsBenhNhan.tuyChon"),
    },
  ];

  return (
    <Main>
      <BaseSearch
        cacheData={state}
        dataInput={[
          {
            widthInput: "232px",
            type: "dateOptions",
            functionChangeInput: (e) => {
              onChange({
                tuThoiGianTrinhKy: e.tuThoiGianTrinhKy?.format(
                  "YYYY-MM-DD 00:00:00"
                ),
                denThoiGianTrinhKy: e.denThoiGianTrinhKy?.format(
                  "YYYY-MM-DD 23:59:59"
                ),
              });
            },
            keyValueInput: ["tuThoiGianTrinhKy", "denThoiGianTrinhKy"],
            title: t("kySo.thoiGianTrinhKy"),
            placeholder: [t("common.tuNgay"), t("common.denNgay")],
            format: "DD/MM/YYYY",
            setState: setState,
            state,
            ref: refShowDate,
            listOptionsCustom: listOptionCustom,
          },
          {
            widthInput: "232px",
            type: "dateOptions",
            setState: setState,
            state,
            functionChangeInput: (e) => {
              onChange({
                tuThoiGianKy: e.tuThoiGianKy?.format("YYYY-MM-DD 00:00:00"),
                denThoiGianKy: e.denThoiGianKy?.format("YYYY-MM-DD 23:59:59"),
              });
            },
            keyValueInput: ["tuThoiGianKy", "denThoiGianKy"],
            title: t("kySo.thoiGianKy"),
            placeholder: [t("common.tuNgay"), t("common.denNgay")],
            format: "DD/MM/YYYY",
            ref: refShowDate1,
          },
          {
            widthInput: "300px",
            placeholder: t("kySo.timHoVaTenNbMaHs"),
            functionChangeInput: onChange,
            isScanQR: true,
            qrGetValue: "maHoSo",
            keysFlexible: [
              {
                key: "tenNb",
                type: "string",
              },
              {
                key: "maHoSo",
                type: "maHoSo",
              },
            ],
          },
          {
            widthInput: "300px",
            placeholder: t("kySo.nhapDeTimTheoTenNguoiTrinhKy"),
            keyValueInput: "tenNguoiTrinhKy",
            functionChangeInput: onChange,
          },
          dataTHIET_LAP_LOC_TAI_KHOAN_BAC_SI?.eval()
            ? {
                widthInput: "250px",
                placeholder: t("kySo.tenNguoiKy"),
                keyValueInput: "nguoiKyId",
                functionChangeInput: onChange,
                type: "select",
                listSelect: listAllNhanVien,
                value: state.nguoiKyId,
              }
            : {
                widthInput: "280px",
                placeholder: t("kySo.nhapDeTimTeoTenNguoiKy"),
                keyValueInput: "tenNguoiKy",
                functionChangeInput: onChange,
              },
          {
            widthInput: "120px",
            placeholder: t("danhMuc.loaiPhieu"),
            keyValueInput: "dsLoaiBaoCaoId",
            functionChangeInput: debounce((e) => {
              baoCaotongHop({
                page: "",
                size: "",
                ...e,
              });
              onChange({
                ...e,
              });
            }, 500),
            type: "selectCheckbox",
            title: t("danhMuc.loaiPhieu"),
            listSelect: listAllLoaiPhieu,
            virtual: true,
            defaultValue: state.dsLoaiBaoCaoId,
            popoverMinWidth: "150px",
          },
          {
            widthInput: "250px",
            placeholder: t("kySo.tenPhieu"),
            keyValueInput: "dsBaoCaoId",
            type: "virtualizedSelect",
            showSearch: true,
            showConfirmButton: true,
            options: listPhieu,
            dropdownWidth: 300,
            value: state.dsBaoCaoId,
            functionChangeInput: ({ dsBaoCaoId }) => {
              onChange({
                dsBaoCaoId,
              });
              setState({ dsBaoCaoId });
            },
          },
          {
            widthInput: "120px",
            placeholder: t("kySo.trangThaiKy"),
            keyValueInput: "dsTrangThai",
            functionChangeInput: debounce(onChangeStatus, 500),
            type: "selectCheckbox",
            title: t("kySo.trangThaiKy"),
            listSelect: listTrangThaiKy,
            virtual: true,
            defaultValue: state.dsTrangThai,
            popoverMinWidth: "150px",
          },
          {
            widthInput: "250px",
            placeholder: t("hsba.trangThaiBenhAn"),
            keyValueInput: "dsTrangThaiBenhAn",
            type: "virtualizedSelect",
            showSearch: true,
            showConfirmButton: true,
            options: dataTrangThaiBenhAn,
            value: state.dsTrangThaiBenhAn,
            functionChangeInput: ({ dsTrangThaiBenhAn }) => {
              onChange({
                dsTrangThaiBenhAn,
              });
              setState({ dsTrangThaiBenhAn });
            },
          },
          {
            widthInput: "250px",
            placeholder: t("baoCao.khoaNB"),
            keyValueInput: "dsKhoaNbId",
            type: "virtualizedSelect",
            showSearch: true,
            showConfirmButton: true,
            value: state.dsKhoaNbId,
            options: listKhoaMemo,
            functionChangeInput: ({ dsKhoaNbId }) => {
              onChange({
                dsKhoaNbId,
              });
              setState({ dsKhoaNbId });
            },
          },
          {
            widthInput: "250px",
            placeholder: t("baoCao.loaiDoiTuong"),
            keyValueInput: "dsLoaiDoiTuongId",
            type: "virtualizedSelect",
            showSearch: true,
            showConfirmButton: true,
            value: state.dsLoaiDoiTuongId,
            options: listAllLoaiDoiTuong,
            functionChangeInput: ({ dsLoaiDoiTuongId }) => {
              onChange({
                dsLoaiDoiTuongId,
              });
              setState({ dsLoaiDoiTuongId });
            },
          },
          {
            widthInput: "250px",
            placeholder: t("common.trangThaiNb"),
            keyValueInput: "dsTrangThaiNb",
            type: "virtualizedSelect",
            showSearch: true,
            showConfirmButton: true,
            value: state.dsTrangThaiNb,
            options: listTrangThaiNb,
            functionChangeInput: ({ dsTrangThaiNb }) => {
              onChange({
                dsTrangThaiNb,
              });
              setState({ dsTrangThaiNb });
            },
          },
          {
            widthInput: "232px",
            type: "dateOptions",
            setState: setState,
            state,
            functionChangeInput: (e) => {
              onChange({
                tuThoiGianRaVien: e.tuThoiGianRaVien?.format(
                  "YYYY-MM-DD 00:00:00"
                ),
                denThoiGianRaVien: e.denThoiGianRaVien?.format(
                  "YYYY-MM-DD 23:59:59"
                ),
              });
            },
            keyValueInput: ["tuThoiGianRaVien", "denThoiGianRaVien"],
            title: t("baoCao.ngayRaVien"),
            placeholder: [t("common.tuNgay"), t("common.denNgay")],
            format: "DD/MM/YYYY",
            ref: refShowDate2,
            listOptionsCustom: listOptionCustom,
          },
          ...(showKyHangLoat
            ? [
                {
                  widthInput: "160px",
                  type: "addition",
                  component: (
                    <div style={{ paddingLeft: 20 }}>
                      <Button
                        rightIcon={
                          <SVG.IcTrinhKy color={"var(--color-blue-primary)"} />
                        }
                        height={36}
                        onClick={onKyHangLoat}
                      >
                        {t("kySo.kyHangLoat")}
                      </Button>
                    </div>
                  ),
                },
              ]
            : []),
        ]}
      />
    </Main>
  );
};

export default TimKiem;
