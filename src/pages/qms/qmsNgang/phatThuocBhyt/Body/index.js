import React, { useMemo, memo } from "react";
import BottomContent from "./BottomContent";
import SlideText from "pages/qms/components/SlideText";

function Body({ quayTiepDon, listAllKhuVuc }) {
  const {
    dsDangThucHien = [],
    dsTiepTheo = [],
    dsDoiTuong = [],
    ten,
    khuVucId,
  } = quayTiepDon || {};

  const khuVuc = listAllKhuVuc.find((i) => i.id === khuVucId)?.ten || "";
  const isPriority = useMemo(() => {
    if (!Array.isArray(dsDoiTuong) || dsDoiTuong?.length === 0) return false;

    return dsDoiTuong?.length === 1 && dsDoiTuong[0].doiTuong === 3;
  }, [dsDoiTuong]);

  return (
    <div className="content">
      <div className="box">
        <div className="box-content box-content__container">
          <div className="header-content">
            <div className={`title ${isPriority ? "priority" : ""}`}>
              <SlideText type={1}>{khuVuc}</SlideText>
            </div>
          </div>
          <BottomContent data={dsTiepTheo} variant="choPhatThuoc" />
        </div>
        <div className="box-content box-content__container">
          <div className="header-content">
            <div className={`title ${isPriority ? "priority" : ""}`}>
              <SlideText type={1}>{ten || ""}</SlideText>
            </div>
          </div>
          <BottomContent data={dsDangThucHien} variant="dangPhatThuoc" />
        </div>
      </div>
    </div>
  );
}

export default memo(Body);
