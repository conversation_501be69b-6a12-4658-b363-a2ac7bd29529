import React from "react";
import { SVG } from "assets";
import { addPrefixNumberZero } from "utils";
import SlideText from "pages/qms/components/SlideText";
import { t } from "i18next";
import classNames from "classnames";

function BottomContent({ data = [], variant = "choPhatThuoc" }) {
  const isChoPhatThuoc = variant === "choPhatThuoc";

  return (
    <div className={classNames("bottom-content", `bottom-content--${variant}`)}>
      <div className="bottom-box">
        <div className="bottom-box__header">
          <div className="bottom-box__title">
            <SVG.IcExtend />
            <span>
              {isChoPhatThuoc ? t("qms.choPhatThuoc") : t("qms.dangPhatThuoc")}
            </span>
          </div>
          <div className="bottom-box__length">
            <span>
              ({data.length} {t("kiosk.nb")})
            </span>
          </div>
        </div>
        <div className="bottom-box__body">
          {data.slice(0, 5).map((item, index) => {
            const _ngaySinh = item.chiNamSinh
              ? item.ngaySinh2 || ""
              : item.ngaySinh
              ? moment(item.ngaySinh).format("YYYY")
              : item.ngaySinh2 || "";

            return (
              <div key={index} className="box-item">
                <div className="box-item__bottom">
                  <div className="box-item__number">
                    <span>{addPrefixNumberZero(item.stt, 4)}</span>
                  </div>
                  <div className="box-item__name">
                    <SlideText type={1}>
                      <span>{item.tenNb}</span>
                    </SlideText>
                  </div>
                </div>
                <div className="box-item__old">{_ngaySinh}</div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
export default BottomContent;
