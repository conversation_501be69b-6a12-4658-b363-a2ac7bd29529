import React, { memo, useRef } from "react";
import { Main } from "./styled";
import { Row, Col } from "antd";
import Modal from "pages/qms/qmsNgang/Modal";
import SlideText from "pages/qms/components/SlideText";
import { useDispatch, useSelector } from "react-redux";

const Header = (props) => {
  const { currentKiosk } = props;
  const refInfo = useRef(null);
  const { logo, tenBenhVien } = useSelector((state) => state.application);

  const {
    quayTiepDon: { getListTongHop },
  } = useDispatch();

  const onOpenPopup = () => {
    getListTongHop({
      khoaId: currentKiosk?.khoaId,
      active: true,
      page: "",
      size: "",
    });
    if (currentKiosk)
      refInfo.current &&
        refInfo.current.show({
          item: currentKiosk,
          loaiQms: currentKiosk.loaiQms,
        });
  };
  return (
    <Main>
      <Row>
        <Col span={24}>
          <Row>
            <div className="header">
              <div className="logo" onClick={onOpenPopup}>
                <img src={logo} alt="..." />
              </div>
              <div className="title-header">
                <SlideText className="title-header__first">
                  <span>{tenBenhVien}</span>
                </SlideText>
              </div>
            </div>
          </Row>
        </Col>
      </Row>
      <Modal ref={refInfo}></Modal>
    </Main>
  );
};

export default memo(Header);
