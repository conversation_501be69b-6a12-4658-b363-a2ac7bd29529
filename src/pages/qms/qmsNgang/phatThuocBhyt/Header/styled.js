import styled from "styled-components";

export const Main = styled.div`
  background: #fff;
  .content {
    float: right;
    text-transform: uppercase;
    font-style: normal;
    font-weight: 800;
    font-size: calc(42vw / 19.2);
    padding-right: calc(50vw / 19.2);
  }

  .header {
    width: 100%;
    display: flex;
    border-radius: 0px calc(16vw / 19.2) calc(16vw / 19.2) 0px;
    justify-content: center;
    align-items: center;
    padding: calc(10vw / 19.2);
    .logo {
      cursor: pointer;
      display: flex;
      align-items: center;

      img {
        width: auto;
        height: calc(70vw / 19.2);
        object-fit: contain;
      }
    }
    .title-header {
      padding-left: calc(10vw / 19.2);
      color: #082a55;
      display: flex;
      flex-direction: column;
      &__first {
        text-transform: uppercase;
        font-style: normal;
        font-weight: 800;
        font-size: calc(40vw / 19.2);
      }
      &__icon {
        width: auto;
        object-fit: contain;
        margin-left: calc(10vw / 19.2);
      }
      &__second {
        max-width: 600px;
        line-height: 56px;

        font-style: normal;
        font-weight: normal;
        font-size: 36px;
      }
    }
  }
`;
