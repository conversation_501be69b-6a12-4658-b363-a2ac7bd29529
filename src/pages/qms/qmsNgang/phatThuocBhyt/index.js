import React, { useEffect, useMemo, useState } from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { Main, QuayPhatThuoc } from "./styled";
import Bottom from "pages/qms/components/Bottom";
import Header from "./Header";
import {
  useInterval,
  useStore,
  useQueryString,
  useCache,
  useThietLap,
} from "hooks";
import { CACHE_KEY, THIET_LAP_CHUNG } from "constants/index";
import { getIntervalMs } from "utils/index";
import Body from "./Body";

const PhatThuocBhyt = (props) => {
  let [kioskId] = useQueryString("kioskId", "");
  const { t } = useTranslation();
  const [listQuay, setListQuay] = useState([]);

  const {
    kiosk: { getById },
    qms: { getDanhSachNbQms, getGoiSoConfig },
    khuVuc: { getListAllKhuVuc },
  } = useDispatch();

  const [isofhToolUrl, _, loadFinish] = useCache(
    "",
    CACHE_KEY.ISOFHTOOL_URL,
    "",
    false
  );
  const [dataTHOI_GIAN_RELOAD_MAN_HINH_QMS] = useThietLap(
    THIET_LAP_CHUNG.THOI_GIAN_RELOAD_MAN_HINH_QMS,
    120,
    {
      refetchInterval: 5 * 60 * 1000,
    }
  );
  const [dataTHOI_GIAN_LAM_MOI_DU_LIEU_QMS] = useThietLap(
    THIET_LAP_CHUNG.THOI_GIAN_LAM_MOI_DU_LIEU_QMS,
    5
  );

  const currentKiosk = useStore("kiosk.currentKiosk", {});
  const listAllKhuVuc = useStore("khuVuc.listAllKhuVuc", []);

  useEffect(() => {
    getById(kioskId);
    getListAllKhuVuc(
      {
        active: true,
        page: "",
        size: "",
      },
      {
        saveCache: false,
      }
    );
  }, []);

  useEffect(() => {
    if (loadFinish) {
      getGoiSoConfig();
    }
  }, [loadFinish]);

  useInterval(() => {
    getById(kioskId);
  }, 30000);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      window.location.reload();
    }, getIntervalMs(dataTHOI_GIAN_RELOAD_MAN_HINH_QMS, 120, "minute"));

    return () => {
      clearTimeout(timeoutId);
    };
  }, [dataTHOI_GIAN_RELOAD_MAN_HINH_QMS]);

  const kiosk = useMemo(() => {
    return currentKiosk && Object.keys(currentKiosk).length > 0
      ? currentKiosk
      : null;
  }, [currentKiosk]);

  const dsQuay2 = useMemo(() => {
    if (!kiosk) return [];
    return [...kiosk.dsQuay, ...kiosk.dsQuayGoiSo].filter(
      (item, index, list) => {
        return list.findIndex((item2) => item2.id == item.id) == index;
      }
    );
  }, [kiosk]);

  const getData = (kiosk) => {
    if (kiosk) {
      getDanhSachNbQms({
        dsQuayId: dsQuay2.map((item) => item.id),
        loaiQms: kiosk.loaiQms,
      }).then((s) => {
        const dsQuay = s.map((item) => {
          const quay = dsQuay2.find((quay) => quay.id === item.quayId) || {};
          return {
            ...item,
            dsDoiTuong: quay?.dsDoiTuong,
            khuVucId: quay?.khuVucId,
            ten: quay?.ten,
            loaiQms: kiosk.loaiQms,
            tenGoiSo: quay?.tenGoiSo,
          };
        });
        setListQuay(dsQuay);
      });
    }
  };

  useEffect(() => {
    if (kiosk) getData(kiosk);
  }, [kiosk]);

  useInterval(() => {
    getData(kiosk);
  }, getIntervalMs(dataTHOI_GIAN_LAM_MOI_DU_LIEU_QMS, 5, "second"));

  return (
    <Main>
      <Header currentKiosk={currentKiosk}></Header>
      {dsQuay2?.length === 1 ? (
        <>
          {dsQuay2.map((quay, index) => {
            const quayTiepDon = (listQuay || []).find(
              (item) => item.quayId === quay.id
            );
            const isHidden = !kiosk.dsQuay.find((item) => item.id == quay.id);
            if (quayTiepDon) {
              return (
                <Body
                  key={index}
                  quayTiepDon={quayTiepDon}
                  currentKiosk={currentKiosk}
                  isHidden={isHidden}
                  listAllKhuVuc={listAllKhuVuc}
                />
              );
            }
          })}
        </>
      ) : (
        <QuayPhatThuoc></QuayPhatThuoc>
      )}
      <Bottom className="footer" />
    </Main>
  );
};

PhatThuocBhyt.propTypes = {};

export default PhatThuocBhyt;
