import styled from "styled-components";

export const Main = styled.div`
  padding: calc(20vmin / 10.8);
  display: flex;
  flex-direction: column;
  height: 100%;
  .content {
    background: #ffffff;
    border: 2px solid #069ba7;
    box-shadow: 0px 0px 15px rgba(9, 30, 66, 0.07);
    border-radius: min(calc(16vmin / 10.8), 32px);
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .title-content {
      display: flex;
      align-items: center;
      justify-content: center;
      font-style: normal;
      font-weight: 800;
      font-size: calc(40vmin / 10.8);
      text-align: center;
      color: #172b4d;
      margin: min(calc(20vmin / 10.8), 20px) 0;
    }
    .box {
      width: 100%;
      padding: calc(20vmin / 10.8);
      display: flex;
      flex: 1;
      gap: calc(20vmin / 10.8);
      @media screen and (orientation: portrait) {
        flex-direction: column-reverse;
      }
      .box-content {
        flex: 1;
        background: #ffffff;
        display: flex;
        flex-direction: column;
        overflow: hidden;
        box-shadow: 0px 2px 10px rgba(0, 0, 0, 0.1);
        border-radius: calc(32vmin / 10.8);
        &__container {
          .title {
            text-align: center;
            font-weight: 900;
            font-size: calc(80vmin / 10.8);
            text-align: center;
            background: #028792;
            color: #ffffff;
            padding: 0 calc(10vmin / 10.8);
            &.priority {
              background: #d84343;
            }
          }
          .bottom-content {
            display: flex;
            border: 3px solid #069ba7;
            border-top: none;
            border-radius: 0 0 calc(32vmin / 10.8) calc(32vmin / 10.8);
            flex: 1;
            overflow: hidden;
            .bottom-box {
              width: 100%;
              height: 100%;
              overflow: hidden;
              background: #ffffff;
              box-shadow: 0px 0px 15px rgba(9, 30, 66, 0.07);
              background: linear-gradient(
                  0deg,
                  rgba(255, 255, 255, 0.95),
                  rgba(255, 255, 255, 0.95)
                ),
                #0762f7;
              border-radius: 0 0 calc(32vmin / 10.8) calc(32vmin / 10.8);
              display: flex;
              flex-direction: column;
              &__header {
                display: flex;
                align-items: center;
                padding: 0 calc(18vmin / 10.8) 0 calc(28vmin / 10.8);
                border-bottom: 2.88509px solid #dce2f2;
                background-color: #fff;
              }
              &__title {
                color: #00a91b;
                font-weight: 600;
                font-size: calc(60vmin / 10.8);
                display: flex;
                align-items: center;
                svg {
                  width: calc(60vmin / 10.8);
                  height: calc(60vmin / 10.8);
                  & path {
                    fill: #00a91b;
                  }
                }
                span {
                  text-transform: uppercase;
                }
              }
              &__length {
                font-family: "Roboto";
                font-style: normal;
                text-align: right;
                font-weight: 900;
                font-size: calc(44vmin / 10.8);
                margin-left: auto;
                color: #172b4d;
              }
              &__body {
                flex: 1;
                background-color: #fff;
                padding: 0 calc(18vmin / 10.8) 0 calc(28vmin / 10.8);
                display: flex;
                flex-direction: column;
                .box-item {
                  display: flex;
                  flex: 0 0 20%;
                  justify-content: space-between;
                  align-items: center;
                  margin: 0;
                  border-bottom: 1px solid #dce2f2;
                  box-sizing: border-box;
                  width: auto;
                  &__bottom {
                    display: flex;
                    align-items: center;
                    flex: 1;
                    overflow: hidden;
                  }
                  &__number {
                    font-family: "Roboto";
                    font-style: normal;
                    font-weight: 800;
                    font-size: calc(44vmin / 10.8);
                    color: #00a91b;
                    margin-left: calc(10vmin / 10.8);
                  }
                  &__name {
                    flex: 1;
                    font-weight: 500;
                    font-size: calc(44vmin / 10.8);
                    text-transform: uppercase;
                    margin-left: calc(10vmin / 4.8);
                    color: #082a55;
                  }
                  &__old {
                    color: #7a869a;
                    min-width: calc(120vmin / 10.8);
                    text-align: right;
                    font-family: "Roboto";
                    font-style: normal;
                    font-weight: 400;
                    font-size: calc(44vmin / 10.8);
                    color: #7a869a;
                    margin-left: calc(10vmin / 10.8);
                  }
                }
              }
              &__body {
                .box-item:last-child {
                  border-bottom: none;
                }
              }
            }
            &--dangPhatThuoc .bottom-box__title {
              svg {
                & path {
                  fill: #172b4d;
                }
              }
              span {
                color: #172b4d;
              }
            }
          }
        }
      }
    }
  }
`;

export const QuayPhatThuoc = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
`;
