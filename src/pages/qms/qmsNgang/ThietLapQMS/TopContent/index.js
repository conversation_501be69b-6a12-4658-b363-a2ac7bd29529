import React, { useEffect, useMemo, useRef, useState } from "react";
import { useTranslation } from "react-i18next";
import { useDispatch, useSelector } from "react-redux";
import { useEnum, useQueryString, useStore } from "hooks";
import { Select } from "components";
import {
  ENUM,
  LIST_LOAI_DANH_SACH_QMS,
  LIST_LOAI_QMS,
  LOAI_KHU_VUC,
  LOAI_PHONG,
} from "constants/index";
import { Main, GlobalStyle } from "./styled";

const TopContent = ({ span = 12, ...props }) => {
  const [loaiQms, setLoaiQMS] = useQueryString("loaiQms", "");
  const [dsPhongId, setPhongId] = useQueryString("dsPhongId", "");
  const [dsQuayId, setQuayTiepDonId] = useQueryString("dsQuayId", "");
  const [loaiDanhSach, setLoaiDanhSach] = useQueryString("loaiDanhSach", "");
  const [dsKhoaId, setDsKhoaId] = useQueryString("dsKhoaId", "");
  const [khuVucId, setKhuVucId] = useQueryString("khuVucId", "");
  const width = useStore("application.width", 0);
  const refPayload = useRef({});
  const listAllPhong = useSelector((state) => state.phong.listAllPhong);
  const listQuayTiepDonTongHop = useSelector(
    (state) => state.quayTiepDon.listQuayTiepDonTongHop
  );

  const listAllKhuVuc = useStore("khuVuc.listAllKhuVuc", []);
  const listAllKhoa = useSelector((state) => state.khoa.listAllKhoa);
  const { t } = useTranslation();
  const {
    phong: { getListAllPhong },
    kiosk: { getListAllKiosk },
    khoa: { getListAllKhoa },
    khuVuc: { getListAllKhuVuc },
    quayTiepDon: { getListTongHop },
  } = useDispatch();

  const [listLoaiQms] = useEnum(ENUM.LOAI_QMS);
  const [listLoaiDsQms] = useEnum(ENUM.LOAI_DANH_SACH_QMS);

  const [state, _setState] = useState({
    loaiQms: loaiQms ? Number(loaiQms) : null,
    dsPhongId: dsPhongId ? Number(dsPhongId) : null,
    dsQuayId: dsQuayId ? Number(dsQuayId) : null,
    loaiDanhSach: loaiDanhSach ? Number(loaiDanhSach) : null,
    dsKhoaId: dsKhoaId
      ? loaiDanhSach == LIST_LOAI_DANH_SACH_QMS.KHU_VUC
        ? (dsKhoaId + "").split(",").map(Number)
        : Number(dsKhoaId)
      : null,
    khuVucId: khuVucId ? Number(khuVucId) : null,
  });
  const setState = (data) => {
    _setState((state) => {
      return { ...state, ...data };
    });
  };

  useEffect(() => {
    getListTongHop({
      dsLoai: [
        state.loaiQms === LIST_LOAI_QMS.QMS_PHAT_THUOC_BHYT
          ? 30
          : state.loaiQms === LIST_LOAI_QMS.QMS_THU_NGAN
          ? 20
          : 10,
      ],
      active: true,
      page: "",
      size: "",
    });
    getListAllPhong({
      page: "",
      size: "",
      active: true,
      ...(state.loaiQms === LIST_LOAI_QMS.QMS_KHAM_BENH
        ? {
          dsLoaiPhong: [state.loaiQms, LOAI_PHONG.PHONG_KHAM_TIEM_CHUNG],
        }
        : { loaiPhong: state.loaiQms }),
    });
    getListAllKhoa({
      page: "",
      size: "",
      active: true,
    });
    if (state.loaiQms === LIST_LOAI_QMS.QMS_PTTT || state.loaiQms === LIST_LOAI_QMS.QMS_THU_NGAN) {
      getListAllKhuVuc(
        {
          active: true,
          loai: state.loaiQms === LIST_LOAI_QMS.QMS_PTTT ? LOAI_KHU_VUC.PTTT : LOAI_KHU_VUC.THU_NGAN, //loai PTTT
          page: "",
          size: "",
        },
        { saveCache: false }
      );
    }
  }, [state.loaiQms]);

  useEffect(() => {
    if (
      state.loaiQms ||
      state.dsPhongId ||
      state.dsQuayId ||
      state.loaiDanhSach ||
      state.dsKhoaId ||
      state.khuVucId
    ) {
      refPayload.current.loaiQms = state.loaiQms;
      refPayload.current.dsPhongId = state.dsPhongId;
      refPayload.current.dsQuayId = state.dsQuayId;
      refPayload.current.loaiDanhSach = state.loaiDanhSach;
      refPayload.current.dsKhoaId = state.dsKhoaId;
      refPayload.current.khuVucId = state.khuVucId;
      props.onSearch && props.onSearch(refPayload.current);
      getListAllKiosk({
        ...refPayload.current,
        page: "",
        size: "",
      }, { saveCache: false });
    }
  }, [
    state.loaiQms,
    state.dsPhongId,
    state.dsQuayId,
    state.dsKhoaId,
    state.loaiDanhSach,
    state.khuVucId,
  ]);


  const listPhong = useMemo(() => {
    if (state.dsKhoaId) {
      return listAllPhong.filter((item) =>
        Array.isArray(state.dsKhoaId)
          ? state.dsKhoaId.includes(item.khoaId)
          : item.khoaId === state.dsKhoaId
      );
    }
    return listAllPhong;
  }, [listAllPhong, state.dsKhoaId]);

  const onChange = (key) => async (e) => {
    if (key === "loaiQms") {
      setLoaiQMS(e);
      setPhongId(null);
      setQuayTiepDonId(null);
      setLoaiDanhSach(null);
      setDsKhoaId(null);
      setKhuVucId(null);
      setState({
        [key]: e,
        dsPhongId: null,
        dsQuayId: null,
        loaiDanhSach: null,
        dsKhoaId: null,
      });
      return;
    } else if (key === "loaiDanhSach") {
      setLoaiDanhSach(e);
      setPhongId(null);
      setDsKhoaId(null);
      setKhuVucId(null);
      setState({
        [key]: e,
        dsPhongId: null,
        dsKhoaId: null,
      });
      return;
    } else if (key === "dsQuayId") setQuayTiepDonId(e);
    else if (key === "dsPhongId") setPhongId(e);
    else if (key === "dsKhoaId") {
      setDsKhoaId(e);
      setState({
        [key]: e,
        dsPhongId: null,
      });
      return;
    } else if (key === "khuVucId") {
      setKhuVucId(e);
      setState({
        [key]: e,
        dsPhongId: null,
        dsKhoaId: null,
      });
      return;
    }
    setState({ [key]: e });
  };

  return (
    <Main span={width > 1080 ? span : 24}>
      <GlobalStyle />
      <div className="top">{t("qms.heThongXepHangCho")}</div>
      <div className="middle">{t("qms.xinKinhChaoQuyKhach")}</div>
      <div className="bottom">{t("qms.thietLap")}</div>
      <Select
        value={state.loaiQms}
        dropdownClassName="modal-select"
        placeholder={t("qms.chonLoaiQms")}
        data={listLoaiQms}
        onChange={onChange("loaiQms")}
      />

      {/* nếu là qms thu ngân hoặc pttt thì cho phép chọn loại danh sách */}
      {(state.loaiQms == LIST_LOAI_QMS.QMS_PTTT || state.loaiQms == LIST_LOAI_QMS.QMS_THU_NGAN) && (
        <>
          <Select
            dropdownClassName="modal-select"
            placeholder={t("qms.chonLoaiDanhSach")}
            data={(listLoaiDsQms || []).filter(item => (state.loaiQms == LIST_LOAI_QMS.QMS_PTTT || [LIST_LOAI_DANH_SACH_QMS.THEO_PHONG, LIST_LOAI_DANH_SACH_QMS.KHU_VUC].includes(item.id)))}
            value={state.loaiDanhSach}
            onChange={onChange("loaiDanhSach")}
          />
          {state.loaiDanhSach == LIST_LOAI_DANH_SACH_QMS.KHU_VUC && (
            <Select
              dropdownClassName="modal-select"
              placeholder={t("danhMuc.chonKhuVuc")}
              data={listAllKhuVuc || []}
              value={state.khuVucId}
              onChange={onChange("khuVucId")}
            />
          )}
          {/* Nếu không phải qms thu ngân */}
          {state.loaiQms !== LIST_LOAI_QMS.QMS_THU_NGAN && state.loaiDanhSach &&
            state.loaiDanhSach !== LIST_LOAI_DANH_SACH_QMS.TOAN_VIEN && (
              <Select
                dropdownClassName="modal-select"
                data={listAllKhoa}
                onChange={onChange("dsKhoaId")}
                value={state.dsKhoaId}
                placeholder={t("common.chonKhoa")}
                {...(state.loaiDanhSach == LIST_LOAI_DANH_SACH_QMS.KHU_VUC && {
                  mode: "multiple",
                  value: state.dsKhoaId || [],
                })}
              ></Select>
            )}
        </>
      )}

      {state.loaiQms != LIST_LOAI_QMS.QMS_TIEP_DON &&
        state.loaiQms != LIST_LOAI_QMS.QMS_THU_NGAN &&
        state.loaiQms != LIST_LOAI_QMS.QMS_PHAT_THUOC_BHYT &&
        (state.loaiQms === LIST_LOAI_QMS.QMS_PTTT
          ? state.loaiDanhSach &&
          state.loaiDanhSach !== LIST_LOAI_DANH_SACH_QMS.TOAN_VIEN
          : true) && (
          <Select
            value={state.dsPhongId}
            dropdownClassName="modal-select"
            placeholder={t("common.chonPhong")}
            data={listPhong}
            onChange={onChange("dsPhongId")}
          />
        )}

      {(state.loaiQms == LIST_LOAI_QMS.QMS_TIEP_DON ||
        state.loaiQms == LIST_LOAI_QMS.QMS_PHAT_THUOC_BHYT ||
        // nếu là qms thu ngân và loại danh sách là theo phòng thì cho phép chọn quầy
        (state.loaiQms == LIST_LOAI_QMS.QMS_THU_NGAN ? state.loaiDanhSach &&
          state.loaiDanhSach == LIST_LOAI_DANH_SACH_QMS.THEO_PHONG
          : true)) && (
          <Select
            dropdownClassName="modal-select"
            placeholder={t(
              state.loaiQms === LIST_LOAI_QMS.QMS_THU_NGAN
                ? "qms.quayThuNgan"
                : state.loaiQms === LIST_LOAI_QMS.QMS_PHAT_THUOC_BHYT
                ? "qms.quayPhatThuocBhyt"
                : "qms.quayTiepDon"
            )}
            data={listQuayTiepDonTongHop}
            value={state.dsQuayId}
            onChange={onChange("dsQuayId")}
          />
        )}
    </Main>
  );
};

export default TopContent;
