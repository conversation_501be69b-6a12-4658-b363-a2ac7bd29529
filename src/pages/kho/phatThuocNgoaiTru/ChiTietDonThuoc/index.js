import React, { useEffect, useMemo, useRef, useState } from "react";
import { useLocation, useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { useTranslation } from "react-i18next";
import { transformObjToQueryString } from "hooks/useQueryString/queryString";
import { useLayer, useThietLap, useWindowFocus } from "hooks";
import LeftPanel from "./LeftPanel";
import RightPanel from "./RightPanel";
import TrangThai from "pages/kho/components/TrangThai";
import { LOAI_MH_PHU, THIET_LAP_CHUNG } from "constants/index";
import { ChonQuay, Button } from "components";
import { SVG } from "assets";
import { LOAI_QUAY, CACHE_KEY } from "constants/index";
import { MainPage, Main } from "./styled";
import isofhToolProvider from "data-access/isofh-tool-provider";
import { isEmpty } from "lodash";

const ChiTietDonThuoc = (props) => {
  const { id } = useParams();
  const refChonQuayTiepDon = useRef(null);
  const [nhaTamUng, setNhaTamUng] = useState(null);
  const { t } = useTranslation();
  const {
    themMoiThuoc: { macDinh },
    thuocChiTiet: {
      searchDonThuoc,
      updateData: updateDataThuocChiTiet,
      getPhuongThucTT,
    },
    lieuDung: { getListAllLieuDung },
    phatThuocNgoaiTru: { dongQuay },
  } = useDispatch();
  const { state: locationState } = useLocation();
  const { queryString } = locationState || {};

  const [_dataHIEN_THI_QUAY_THUOC_NGOAI_TRU] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_QUAY_THUOC_NGOAI_TRU, "0"
  );

  const dataHIEN_THI_QUAY_THUOC_NGOAI_TRU = useMemo(() => {
    if (_dataHIEN_THI_QUAY_THUOC_NGOAI_TRU == "1")
      return 1;
    if (_dataHIEN_THI_QUAY_THUOC_NGOAI_TRU == "2")
      return 2;
    if (_dataHIEN_THI_QUAY_THUOC_NGOAI_TRU.toLowerCase() == "true")
      return 1;
    return 0;
  }, [_dataHIEN_THI_QUAY_THUOC_NGOAI_TRU])

  const { infoPatient } = useSelector((state) => state.thuocChiTiet);

  const layerId = useLayer();

  const isThemMoi = useMemo(() => {
    return id == undefined;
  }, [id]);
  useEffect(() => {
    macDinh();
    getPhuongThucTT({ page: "", size: "", active: true });
    getListAllLieuDung({ page: "", size: "", active: true });
    return () => {
      updateDataThuocChiTiet({
        // reset chi tiet
        infoPatient: [],
        dsThuocEdit: [],
        selectedDonThuoc: [],
        nguoiBenhId: null,
        isAdvised: false,
      });
    };
  }, []);
  useEffect(() => {
    if (id) {
      searchDonThuoc(id);
    }
  }, [id]);

  const clearManHinhPhu = () => {
    isofhToolProvider.putDataCenter(
      { ma: "TT_NB", value: {} },
      { keepalive: true }
    );
    isofhToolProvider.putDataCenter(
      { ma: "LOAI_MH_PHU", value: null },
      { keepalive: true }
    );
  };
  useEffect(() => {
    //khi đóng trình duyệt thì cũng tự xóa thông tin màn hình phụ
    window.addEventListener("beforeunload", () => {
      clearManHinhPhu();
    });
  }, []);

  useWindowFocus(
    (isFocus) => {
      if (!isFocus) {
        if (window.phatThuocNgoaiTruKhongXoaDuLieuKhiMatFocus) return;
        //khi focus sang màn hình khác thì cũng xóa thông tin màn hình phụ
        clearManHinhPhu();
      } else {
        //ngược lại khi focus thì load lại thông tin và gửi dữ liệu sang màn hình phụ
        if (id) searchDonThuoc(id);
      }
    },
    [id]
  );

  const onClose = () => {
    if (nhaTamUng?.id) {
      dongQuay({ quayHienTai: nhaTamUng?.id }).then((s) => {
        setNhaTamUng(null);
        refChonQuayTiepDon.current && refChonQuayTiepDon.current.setValue(null);
      });
    }
  };
  useEffect(() => {
    if (!isEmpty(infoPatient?.nbThongTinChung)) {
      isofhToolProvider.putDataCenter({
        ma: "LOAI_MH_PHU",
        value: LOAI_MH_PHU.PHAT_THUOC_NGOAI_TRU,
      });
      isofhToolProvider.putDataCenter({
        ma: "TT_NB",
        value: {
          ...infoPatient.nbThongTinChung,
          stt: infoPatient.phieuXuat?.stt || "",
        },
      });
    }
    return () => {
      isofhToolProvider.putDataCenter({
        ma: "TT_NB",
        value: {},
      });
      isofhToolProvider.putDataCenter({
        ma: "DS_CHU_KY",
        value: [],
      });
    };
  }, [infoPatient?.nbThongTinChung]);

  return (
    <MainPage
      breadcrumb={[
        { title: t("kho.kho"), link: "/kho" },
        {
          title: t("kho.phatThuoc.phatThuocNgoaiTruRaVien"),
          link:
            "/kho/phat-thuoc-ngoai-tru" +
            transformObjToQueryString(queryString),
        },
        {
          title: t("kho.thongTinDonThuoc"),
          link: `/kho/phat-thuoc-ngoai-tru/chi-tiet/${id}`,
        },
      ]}
      rightBreadcrumbContent={
        <div className="breadcrumb-right">
          {dataHIEN_THI_QUAY_THUOC_NGOAI_TRU == 1 && (
            <>
              <div className="group-controls">
                <ChonQuay
                  className="chon-quay-phat-thuoc"
                  ref={refChonQuayTiepDon}
                  type={2}
                  dsLoaiQuay={LOAI_QUAY.THU_NGAN}
                  cacheKey={CACHE_KEY.DATA_NHA_TAM_UNG}
                  onChange={(value) => {
                    setNhaTamUng(value);
                  }}
                />
                <Button
                  className="btn-dong-quay"
                  onClick={onClose}
                  type="default"
                  leftIcon={<SVG.IcCancel />}
                  iconHeight={20}
                  height={28}
                  color="#64748B"
                >
                  {t("tiepDon.dongQuay")}
                </Button>
              </div>
            </>
          )}
          <TrangThai
            type={5}
            times={[
              infoPatient?.phieuXuat?.thoiGianTaoPhieu,
              infoPatient?.phieuXuat?.thoiGianDuyet,
            ]}
          />
        </div>
      }
      title={<div style={{ display: "flex" }}>{t("kho.thongTinDonThuoc")}</div>}
    >
      <Main>
        <LeftPanel
          isThemMoi={isThemMoi}
          layerId={layerId}
          nhaTamUng={nhaTamUng}
          dataHIEN_THI_QUAY_THUOC_NGOAI_TRU={dataHIEN_THI_QUAY_THUOC_NGOAI_TRU}
        />
        <RightPanel isThemMoi={isThemMoi} layerId={layerId} />
      </Main>
    </MainPage>
  );
};

export default ChiTietDonThuoc;
