import React, { useEffect, useMemo, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import moment from "moment";
import { useTranslation } from "react-i18next";
import { useLocation, useHistory } from "react-router-dom";
import { Col, message, Row } from "antd";
import { Main, InputSearch } from "./styled";
import {
  GIOI_TINH_BY_VALUE,
  ENUM,
  TRANG_THAI_THUOC,
  HOTKEY,
  THIET_LAP_CHUNG,
} from "constants/index.js";
import { useStore, useEnum, useThietLap } from "hooks";
import PopoverTrangThaiDuyetBH from "./PopoverTrangThaiDuyetBH";
import { SVG } from "assets";
import { InputTimeout } from "components";

const ThongTinNguoiBenh = ({
  layerId,
  nhaTamUng,
  dataHIEN_THI_QUAY_THUOC_NGOAI_TRU,
}) => {
  const { t } = useTranslation();
  const history = useHistory();
  const { onRegisterHotkey } = useDispatch().phimTat;
  const refThongTin = useRef();
  const infoPatient = useStore("thuocChiTiet.infoPatient");
  const [listTrangThaiDuyetBH] = useEnum(ENUM.TRANG_THAI_DUYET_BAO_HIEM);
  const refAutoFocus = useRef(null);
  const { state: locationState } = useLocation();
  const { record } = locationState || {};
  const listTrangThaiDls = Object.values(TRANG_THAI_THUOC);
  const { nbThongTinChung, phieuThu } = infoPatient || {};
  const [CANH_BAO_KHONG_TIM_THAY_NB] = useThietLap(
    THIET_LAP_CHUNG.CANH_BAO_KHONG_TIM_THAY_NB
  );
  const {
    tenNguoiBaoLanh1,
    sdtNguoiBaoLanh1,
    tenNguoiBaoLanh2,
    sdtNguoiBaoLanh2,
    tuNgayTheBhyt,
    denNgayTheBhyt,
    tienConLai,
    tenNb,
    ngaySinh,
    tuoi2,
    gioiTinh,
    soDienThoai,
    diaChi,
    maNb,
    maHoSo,
    maTheBhyt,
    tenKhoaNb,
    moTa,
  } = nbThongTinChung || {};
  const { trangThaiDuyetBaoHiem, dsCdChinh, dsCdKemTheo } = phieuThu || {};

  const {
    phatThuocNgoaiTru: { getListDonThuocNgoaiTru },
    danhSachPhieuThu: { goiNbTiepTheo },
    thuocChiTiet: { updateData },
  } = useDispatch();

  const [state, _setState] = useState({});
  const setState = (data = {}) => _setState((pre) => ({ ...pre, ...data }));

  useEffect(() => {
    onRegisterHotkey({
      layerId,
      hotKeys: [
        {
          keyCode: HOTKEY.F3, //F3
          onEvent: () => {
            refThongTin.current && refThongTin.current.focus();
          },
        },
      ],
    });
  }, []);

  const nguoiBaoLanh = useMemo(() => {
    const pattern = (data) => {
      return data
        .reduce((acc, cur) => {
          if (cur) {
            acc.push(cur);
          }
          return acc;
        }, [])
        .join(" - ");
    };

    let result1 = [tenNguoiBaoLanh1, sdtNguoiBaoLanh1];
    let result2 = [tenNguoiBaoLanh2, sdtNguoiBaoLanh2];
    result1 = pattern(result1);
    result2 = pattern(result2);
    return <span>{`${result1}${result2 ? "; " : ""}${result2}`}</span>;
  }, [tenNguoiBaoLanh1, sdtNguoiBaoLanh1, tenNguoiBaoLanh2, sdtNguoiBaoLanh2]);

  const thoiHanBaoHiemYte = useMemo(() => {
    const tuNgay = tuNgayTheBhyt
      ? moment(tuNgayTheBhyt).format("DD/MM/YYYY")
      : "";
    const denNgay = denNgayTheBhyt
      ? moment(denNgayTheBhyt).format("DD/MM/YYYY")
      : "";
    return <span>{`${tuNgay}${denNgay ? "- " : ""}${denNgay}`}</span>;
  }, [tuNgayTheBhyt, denNgayTheBhyt]);

  const renderTrangThaiDls = useMemo(() => {
    const { trangThaiDls } = record || {};
    let title = "";
    if (trangThaiDls) {
      title = listTrangThaiDls?.find((item) => item.id === trangThaiDls)?.i18;
      if (title) return t(title);
    } else if (trangThaiDls === null) {
      return t("kho.choDuyetDLS");
    } else return "";
  }, [record]);

  const renderChanDoan = useMemo(() => {
    const cdChinh = dsCdChinh?.[0];
    return (
      cdChinh && (
        <>
          {cdChinh.ma} - {cdChinh.ten}
          {dsCdKemTheo?.length > 0
            ? `/ ${dsCdKemTheo
              .map((item) => `${item.ma} - ${item.ten}`)
              .join("; ")}`
            : null}
          {moTa ? `/ ${moTa}` : null}
        </>
      )
    );
  }, [dsCdChinh]);

  const onChange = (key) => (e) => {
    let value = "";
    if (e?.target) {
      if (e.target.hasOwnProperty("checked")) value = e.target.checked;
      else value = e.target.value;
    } else value = e;
    setState({
      [key]: value,
    });
  };
  useEffect(() => {
    refAutoFocus.current && refAutoFocus.current.focus();
    if (state?.qrBN && infoPatient?.phieuXuat?.trangThai === 30) {
      setState({ qrBN: "" });
    }
  }, [infoPatient]);

  const onKeyDown = (e) => {
    if (e.key === "Enter") {
      let str = e.target.value || "";
      let isNumber = /^[0-9]+$/.test(str);
      let isMaNb = /^[a-zA-Z]+[0-9]+$/.test(str);

      let param = {};
      if (isNumber) {
        param = { maHoSo: str };
      } else if (isMaNb) {
        param = { maNb: str };
      } else if (str.startsWith("NB") && str.endsWith("$")) {
        let array = str.split("|");
        let maHoSo = array[3];

        param = { maHoSo };
      }

      if (Object.keys(param).length) {
        getListDonThuocNgoaiTru({
          dataSearch: { ...param },
        })
          .then(async (s) => {
            const checkUniqueNbDotDieuTriI = new Set(
              s?.data?.map((item) => item.nbDotDieuTriId)
            ).size;
            if (
              checkUniqueNbDotDieuTriI === 0 &&
              CANH_BAO_KHONG_TIM_THAY_NB?.eval()
            ) {
              history.push({
                pathname: `/kho/phat-thuoc-ngoai-tru/chi-tiet`,
              });
              if (isMaNb) {
                message.error(
                  `${t("nhaThuoc.khongTimThayThongTinDonThuoc")} ${t(
                    "common.maNb"
                  )?.toLowerCase()}: ` + (param?.maNb || "")
                );
              } else {
                message.error(
                  `${t("nhaThuoc.khongTimThayThongTinDonThuoc")} ${t(
                    "common.maHs"
                  )?.toLowerCase()}: ` + (param.maHoSo || "")
                );
              }
              updateData({
                infoPatient: {},
                nguoiBenhId: {},
                nbThongTinChung: {},
              });
              return;
            }
            if (checkUniqueNbDotDieuTriI !== 1) return;
            const firstItem = s?.data?.[0];

            if (
              param.hasOwnProperty("maHoSo") &&
              nhaTamUng?.khongLaySo &&
              nhaTamUng?.id &&
              firstItem?.nbDotDieuTriId &&
              dataHIEN_THI_QUAY_THUOC_NGOAI_TRU == 1
            ) {
              try {
                await goiNbTiepTheo({
                  quayId: nhaTamUng.id,
                  nbDotDieuTriId: firstItem?.nbDotDieuTriId,
                });
              } catch (error) {
                console.error("Error calling goiNbTiepTheo:", error);
              }
            }
            setTimeout(() => {
              history.push({
                pathname: `/kho/phat-thuoc-ngoai-tru/chi-tiet/${firstItem?.id}`,
              });
              setState({
                qrBN: "",
              });
            }, 300);
          })
          .catch((e) => {
            debugger;
          });
      }
    }
  };

  return (
    <Main>
      <div className="body-info">
        <Row className="info-full">
          <div
            className="title"
            style={{ width: "100%", display: "flex", alignItems: "center" }}
          >
            {t("nhaThuoc.thongTinKhachHang")}:
            <InputSearch>
              <SVG.IcSearch />

              <InputTimeout
                placeholder={t("kho.quetQrNguoiBenhHoacNhapMaHoSoDeTimKiem")}
                onChange={onChange("qrBN")}
                onKeyDown={onKeyDown}
                value={state?.qrBN}
                autoFocus
                ref={refAutoFocus}
              />
            </InputSearch>
          </div>
          <Col sm={16} md={16} xl={16} xxl={16} className="info">
            <Row className="">
              <div className="title" style={{ marginRight: 90 }}>
                {t("common.hoVaTen")}:
              </div>
              <div className="detail">
                <b>
                  <span
                    style={{
                      color: (tienConLai || 0) < 0 ? "#FC3B3A" : "inherit",
                    }}
                  >
                    {tenNb}
                  </span>
                  {` (${ngaySinh
                      ? moment(ngaySinh).format("DD/MM/YYYY") + " - "
                      : ""
                    }${`${tuoi2 || ""}`} - ${gioiTinh ? GIOI_TINH_BY_VALUE[gioiTinh] : ""
                    })`}
                </b>
              </div>
            </Row>
          </Col>
          <Col sm={8} md={8} xl={8} xxl={8} className="info">
            <Row className="">
              <div className="title" style={{ marginRight: 35 }}>
                {t("common.sdt")}:
              </div>
              <div className="detail">{soDienThoai}</div>
            </Row>
          </Col>
          <Col sm={16} md={16} xl={16} xxl={16} className="info">
            <Row className="">
              <div className="title last" style={{ marginRight: 102 }}>
                {t("common.diaChi")}:
              </div>
              <div className="detail last">{diaChi}</div>
            </Row>
          </Col>
          <Col sm={8} md={8} xl={8} xxl={8} className="info">
            <Row className="">
              <div className="title last" style={{ marginRight: 20 }}>
                {t("common.maNb")}
              </div>
              <div className="detail last">{maNb}</div>
            </Row>
          </Col>
          <Col sm={16} md={16} xl={16} xxl={16} className="info">
            <Row className="">
              <div className="title last" style={{ marginRight: 8 }}>
                {t("nhaThuoc.nguoiBaoLanhSdt")}:
              </div>
              <div className="detail last">{nguoiBaoLanh}</div>
            </Row>
          </Col>
          <Col sm={8} md={8} xl={8} xxl={8} className="info">
            <Row className="">
              <div className="title last" style={{ marginRight: 20 }}>
                {t("common.maHs")}:
              </div>
              <div className="detail last">{maHoSo}</div>
            </Row>
          </Col>
          <Col sm={16} md={16} xl={16} xxl={16} className="info">
            <Row className="">
              <div className="title last" style={{ marginRight: 8 }}>
                {t("common.soBHYT")}:
              </div>
              <div className="detail last">{maTheBhyt}</div>
            </Row>
          </Col>
          <Col sm={8} md={8} xl={8} xxl={8} className="info">
            <Row className="">
              <div className="title last" style={{ marginRight: 20 }}>
                {t("common.giaTriThe")}:
              </div>
              <div className="detail last">{thoiHanBaoHiemYte}</div>
            </Row>
          </Col>
          <Col sm={16} md={16} xl={16} xxl={16} className="info">
            <Row className="">
              <div className="title last" style={{ marginRight: 8 }}>
                {t("common.khoa")}:
              </div>
              <div className="detail last">{tenKhoaNb}</div>
            </Row>
          </Col>
          <Col sm={8} md={8} xl={8} xxl={8} className="info">
            <Row className="">
              <div className="title last" style={{ marginRight: 10 }}>
                {t("khth.trangThaiDuyetBH")}:
              </div>
              {trangThaiDuyetBaoHiem == 20 && (
                <div className="detail last" style={{ display: "flex" }}>
                  {
                    (listTrangThaiDuyetBH || []).find(
                      (x) => x.id === trangThaiDuyetBaoHiem
                    )?.ten
                  }{" "}
                  {trangThaiDuyetBaoHiem == 20 && (
                    <div style={{ marginLeft: 5 }}>
                      <PopoverTrangThaiDuyetBH />
                    </div>
                  )}
                </div>
              )}
            </Row>
          </Col>
          <Col sm={16} md={16} xl={16} xxl={16} className="info">
            <Row className="">
              <div className="title last" style={{ marginRight: 8 }}>
                {t("kho.trangThaiDuyetDls")}:
              </div>
              <div className="detail last">{renderTrangThaiDls}</div>
            </Row>
          </Col>
          <Col sm={24} md={24} xl={24} xxl={24} className="info">
            <Row>
              <div className="title last" style={{ marginRight: 8 }}>
                {t("common.chanDoan")}:
              </div>
              <div className="detail last flex1">{renderChanDoan}</div>
            </Row>
          </Col>
        </Row>
      </div>
    </Main>
  );
};

export default ThongTinNguoiBenh;
