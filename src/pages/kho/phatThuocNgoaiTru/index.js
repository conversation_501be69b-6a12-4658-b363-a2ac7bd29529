import React, { useMemo, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import { Menu, message, InputNumber } from "antd";
import TimKiemPhieu from "pages/kho/phatThuocNgoaiTru/TimKiemPhieu";
import DanhSach from "pages/kho/phatThuocNgoaiTru/DanhSach";
import {
  Page,
  Button,
  Dropdown,
  Breadcrumb,
  ChonQuay,
  AuthWrapper,
} from "components";
import { useTranslation } from "react-i18next";
import { useLoading, useStore, useThietLap } from "hooks";
import phieuNhapXuatProvider from "data-access/kho/phieu-nhap-xuat-provider";
import { toSafePromise } from "lib-utils";
import moment from "moment";
import { isArray } from "utils";
import { CACHE_KEY, LOAI_QUAY, ROLES, THIET_LAP_CHUNG } from "constants/index";
import { SVG } from "assets";
import { Main, TitleRightWrapper, BreadCrumbWrapper } from "./styled";

const PhatThuocNgoaiTru = (props) => {
  const { t } = useTranslation();
  const { showLoading, hideLoading } = useLoading();
  const [tuSTT, setTuSTT] = useState("");
  const [denSTT, setDenSTT] = useState("");
  const [nhaTamUng, setNhaTamUng] = useState(null);
  const refDanhSach = useRef(null);
  const refChonQuayTiepDon = useRef(null);
  const dataSearch = useStore("phatThuocNgoaiTru.dataSearch", {});
  const showButtonHuyGiuTon = useStore(
    "phatThuocNgoaiTru.showButtonHuyGiuTon",
    false
  );

  const [_dataHIEN_THI_QUAY_THUOC_NGOAI_TRU] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_QUAY_THUOC_NGOAI_TRU, "0"
  );

  const dataHIEN_THI_QUAY_THUOC_NGOAI_TRU = useMemo(() => {
    if (_dataHIEN_THI_QUAY_THUOC_NGOAI_TRU == "1")
      return 1;
    if (_dataHIEN_THI_QUAY_THUOC_NGOAI_TRU == "2")
      return 2;
    if (_dataHIEN_THI_QUAY_THUOC_NGOAI_TRU.toLowerCase() == "true")
      return 1;
    return 0;
  }, [_dataHIEN_THI_QUAY_THUOC_NGOAI_TRU])





  const {
    phatThuocNgoaiTru: {
      xuatDuLieu,
      onSizeChange,
      inPhieuXuatThuoc,
      getListDonThuocNgoaiTru,
      huyGiuTon,
      updateData,
    },
    danhSachPhieuThu: { dongQuay, goiNbTiepTheo },
  } = useDispatch();

  const onXuatDuLieu = async () => {
    try {
      showLoading();

      await xuatDuLieu({
        ...dataSearch,
        loai: 10,
      });
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onClose = () => {
    if (nhaTamUng?.id) {
      dongQuay({ quayHienTai: nhaTamUng?.id }).then((s) => {
        setNhaTamUng(null);
        refChonQuayTiepDon.current && refChonQuayTiepDon.current.setValue(null);
      });
    }
  };

  const onXuatSTT = async () => {
    const selectedRowKeys = refDanhSach.current?.getSelectedRowKeys();

    const isRowSelected = isArray(selectedRowKeys, 1);

    if (!isRowSelected && (!tuSTT || !denSTT)) {
      message.error(t("kho.nhapTuSoVaDenSo"));
      return;
    }
    showLoading();
    const params = isRowSelected
      ? {
        dsId: selectedRowKeys,
      }
      : {
        tuStt: tuSTT,
        denStt: denSTT,
        thoiGianLaySo: dataSearch?.tuThoiGianThanhToan
          ? moment(dataSearch?.tuThoiGianThanhToan).format("YYYY-MM-DD")
          : undefined,
      };
    const [err, res] = await toSafePromise(
      phieuNhapXuatProvider.guiDuyetSTT(params)
    );
    if (err) {
      return message.error(err.message || t("kho.xuatSttKhongThanhCong"));
    }

    if ((res?.data || []).length > 0) {
      inPhieuXuatThuoc({ dsPhieuNhapXuatId: res.data });
    }
    message.success(t("kho.xuatSttThanhCong"));
    refDanhSach.current?.setSelectedRowKeys([]);
    onSizeChange({ page: 0 });
    hideLoading();
  };

  const onHuyGuiTon = async () => {
    const selectedRowKeys = refDanhSach.current?.getListHuyGiuTon();
    const isRowSelected = isArray(selectedRowKeys, 1);
    if (!isRowSelected) {
      message.error(t("kho.vuiLongChonDonThuoc"));
      return;
    }
    showLoading();
    const [err] = await toSafePromise(huyGiuTon({ body: selectedRowKeys }));
    if (err) {
      hideLoading();
      return message.error(err.message || t("kho.huyGiuTonKhongThanhCong"));
    }
    refDanhSach.current?.setSelectedRowKeys([]);
    updateData({ showButtonHuyGiuTon: false });
    onSizeChange({ page: 0 });
    hideLoading();
  };

  const menu = () => (
    <Menu
      items={[
        {
          key: 5,
          label: (
            <a onClick={onXuatDuLieu}>
              <div className="flex icon_utilities gap-8">
                <SVG.IcDownload />
                <span>{t("common.xuatDuLieu")}</span>
              </div>
            </a>
          ),
        },
      ]}
    />
  );

  return (
    <Page
      style={{ pageBodyPadding: "0" }}
      showBreadcrumb={false}
      topHeader={
        <BreadCrumbWrapper>
          <div className="breadcrumb-left">
            <Breadcrumb
              chains={[
                { title: t("kho.kho"), link: "/kho" },
                {
                  title: t("kho.phatThuoc.phatThuocNgoaiTruRaVien"),
                  link: "/kho/phat-thuoc-ngoai-tru",
                },
              ]}
            />
            <div className="breadcrumb-title">
              {t("kho.phatThuoc.danhSachPhatThuocNgoaiTruRaVien")}
            </div>
          </div>

          <div className="breadcrumb-right">
            <TitleRightWrapper>
              {dataHIEN_THI_QUAY_THUOC_NGOAI_TRU == 1 && (
              <>
                <div className="group-controls">
                  <ChonQuay
                    className="chon-quay-phat-thuoc"
                    ref={refChonQuayTiepDon}
                    type={2}
                    dsLoaiQuay={LOAI_QUAY.THU_NGAN}
                    cacheKey={CACHE_KEY.DATA_NHA_TAM_UNG}
                    onChange={(value) => {
                      setNhaTamUng(value);
                    }}
                  />
                  <Button
                    className="btn-dong-quay"
                    onClick={onClose}
                    type="default"
                    leftIcon={<SVG.IcCancel />}
                    iconHeight={20}
                    height={28}
                    color="#64748B"
                  >
                    {t("tiepDon.dongQuay")}
                  </Button>
                </div>
              </>
              )}
              {showButtonHuyGiuTon && (
                <AuthWrapper
                  accessRoles={[ROLES["PHAT_THUOC_NGOAI_TRU"].HUY_GIU_TON]}
                >
                  <Button type="primary" onClick={onHuyGuiTon} height={28}>
                    {t("kho.huyGiuTon")}
                  </Button>
                </AuthWrapper>
              )}
              <div className="group-input">
                <span style={{ fontWeight: 500 }}>
                  {t("kho.tuSttXuatThuoc")}
                </span>
                <InputNumber
                  placeholder={t("kho.tuSo")}
                  style={{ width: "120px" }}
                  value={tuSTT}
                  onChange={(val) => setTuSTT(val)}
                />
              </div>
              <div className="group-input">
                <span style={{ fontWeight: 500 }}>
                  {t("kho.denSttXuatThuoc")}
                </span>
                <InputNumber
                  placeholder={t("kho.denSo")}
                  style={{ width: "120px" }}
                  value={denSTT}
                  onChange={(val) => setDenSTT(val)}
                />
              </div>
              <div className="group-buttons">
                <Button type="primary" onClick={onXuatSTT} height={28}>
                  {t("kho.xuatSTT")}
                </Button>
                <Dropdown
                  overlay={menu}
                  trigger={"click"}
                  placement="bottomRight"
                >
                  <Button
                    rightIcon={<SVG.IcMore />}
                    style={{ marginLeft: "8px" }}
                    height={28}
                  >
                    {t("common.tienIch")}
                  </Button>
                </Dropdown>
              </div>
            </TitleRightWrapper>
          </div>
        </BreadCrumbWrapper>
      }
    >
      <Main>
        <TimKiemPhieu
          nhaTamUng={nhaTamUng}
          goiNbTiepTheo={goiNbTiepTheo}
          getListDonThuocNgoaiTru={getListDonThuocNgoaiTru}
          dataHIEN_THI_QUAY_THUOC_NGOAI_TRU={dataHIEN_THI_QUAY_THUOC_NGOAI_TRU}
        />
        <DanhSach ref={refDanhSach} />
      </Main>
    </Page>
  );
};

export default PhatThuocNgoaiTru;
