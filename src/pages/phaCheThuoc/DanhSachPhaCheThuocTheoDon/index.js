import React, { useEffect, useMemo, useRef, useState } from "react";
import { useDispatch } from "react-redux";
import { useTranslation } from "react-i18next";
import { useLoading, useStore, useThietLap, useRefFunc } from "hooks";
import { checkRole } from "lib-utils/role-utils";
import { Button, Dropdown } from "components";
import {
  MAN_HINH_PHIEU_IN,
  ROLES,
  THIET_LAP_CHUNG,
  TRANG_THAI_PHA_CHE_THUOC,
  VI_TRI_PHIEU_IN,
  MA_BIEU_MAU_EDITOR,
  LOAI_BIEU_MAU,
} from "constants/index";
import BangDanhSach from "./BangDanhSach";
import BoLocTimKiem from "./BoLocTimKiem";
import { MainPage } from "./styled";
import { SVG } from "assets";
import { Menu } from "antd";
import printProvider from "data-access/print-provider";
import { checkIsPhieuKySo } from "utils/phieu-utils";
import { openInNewTab } from "utils";
import ModalChonPhieuBanGiaoThuocNB from "./modals/ModalChonPhieuBanGiaoThuocNB";
import ModalBienBanHuyThuoc from "./modals/ModalBienBanHuyThuoc";
import fileUtils from "utils/file-utils";

const DsPhaCheThuocTheoDon = () => {
  const { t } = useTranslation();
  const refBangDanhSach = useRef(null);
  const refModalChonPhieuBanGiaoThuocNB = useRef(null);
  const refBienBanHuyThuoc = useRef(null);

  const listData = useStore("nbPhaCheThuoc.listData", []);
  const { showLoading, hideLoading } = useLoading();

  const [
    dataHIEN_THI_DUYET_DUOC_VA_XAC_NHAN_NB_PHA_CHE_THUOC_THEO_DON,
    loadFinish,
  ] = useThietLap(
    THIET_LAP_CHUNG.HIEN_THI_DUYET_DUOC_VA_XAC_NHAN_NB_PHA_CHE_THUOC_THEO_DON
  );

  const {
    nbPhaCheThuoc: {
      duyetPhaChe,
      huyDuyetPhaChe,
      giaoPhaChe,
      huyGiaoPhaChe,
      onChangeInputSearch,
      duyetDuyetDuoc,
      huyDuyetDuoc,
      xacNhanNb,
      huyXacNhanNb,
      getListPhieu,
    },
    phieuIn: { getFilePhieuIn, showFileEditor },
  } = useDispatch();

  const [state, _setState] = useState({ dsSelected: [], listPhieu: [] });
  const setState = (data) => {
    _setState((pre) => {
      return { ...pre, ...data };
    });
  };
  const onSelect = (data) => {
    const dsSelected = listData.filter((x) => data.includes(`${x.id}`));
    setState({ dsSelected });
  };

  useEffect(() => {
    getListPhieu({
      maManHinh: MAN_HINH_PHIEU_IN.PHA_CHE_THUOC,
      maViTri: VI_TRI_PHIEU_IN.PHA_CHE_THUOC.IN_GIAY_TO_DS,
    }).then((res) => {
      const arr = res || [];

      arr.sort((a, b) => {
        if (a.stt == null && b.stt == null) return 0;
        if (a.stt == null) return 1;
        if (b.stt == null) return -1;
        return a.stt - b.stt;
      });

      setState({
        listPhieu: arr,
      });
    });
  }, []);

  const {
    visibleDuyetPhaChe,
    visibleHuyDuyetPhaChe,
    visibleGiaoPhaChe,
    visibleHuyGiaoPhaChe,
    visibleXacNhanNb,
    visibleHuyXacNhanNb,
    visibleDuyetDuoc,
    visibleHuyDuyetDuoc,
  } = useMemo(() => {
    let obj = {
      visibleDuyetPhaChe: false,
      visibleHuyDuyetPhaChe: false,
      visibleGiaoPhaChe: false,
      visibleHuyGiaoPhaChe: false,
      visibleXacNhanNb: false,
      visibleHuyXacNhanNb: false,
      visibleDuyetDuoc: false,
      visibleHuyDuyetDuoc: false,
    };
    if (!loadFinish) return obj;

    const isVisible = (trangThai) =>
      !!state.dsSelected?.length &&
      state.dsSelected.every((o) => o.trangThai === trangThai);

    if (dataHIEN_THI_DUYET_DUOC_VA_XAC_NHAN_NB_PHA_CHE_THUOC_THEO_DON?.eval()) {
      // tạo mới
      obj.visibleDuyetDuoc = isVisible(TRANG_THAI_PHA_CHE_THUOC.TAO_MOI);
      // duyệt dược
      obj.visibleXacNhanNb = isVisible(TRANG_THAI_PHA_CHE_THUOC.DUYET_DUOC);
      obj.visibleHuyDuyetDuoc = isVisible(TRANG_THAI_PHA_CHE_THUOC.DUYET_DUOC);
      // xác nhận nb
      obj.visibleDuyetPhaChe = isVisible(TRANG_THAI_PHA_CHE_THUOC.XAC_NHAN_NB);
      obj.visibleHuyXacNhanNb = isVisible(TRANG_THAI_PHA_CHE_THUOC.XAC_NHAN_NB);
      // pha chế
      obj.visibleHuyDuyetPhaChe = isVisible(
        TRANG_THAI_PHA_CHE_THUOC.DA_PHA_CHE
      );
      obj.visibleGiaoPhaChe = isVisible(TRANG_THAI_PHA_CHE_THUOC.DA_PHA_CHE);
      // đã giao
      obj.visibleHuyGiaoPhaChe = isVisible(TRANG_THAI_PHA_CHE_THUOC.DA_GIAO);
    } else {
      obj.visibleDuyetPhaChe = isVisible(TRANG_THAI_PHA_CHE_THUOC.TAO_MOI);
      obj.visibleHuyDuyetPhaChe = isVisible(
        TRANG_THAI_PHA_CHE_THUOC.DA_PHA_CHE
      );
      obj.visibleGiaoPhaChe = isVisible(TRANG_THAI_PHA_CHE_THUOC.DA_PHA_CHE);
      obj.visibleHuyGiaoPhaChe = isVisible(TRANG_THAI_PHA_CHE_THUOC.DA_GIAO);
    }

    return obj;
  }, [
    state.dsSelected,
    loadFinish,
    dataHIEN_THI_DUYET_DUOC_VA_XAC_NHAN_NB_PHA_CHE_THUOC_THEO_DON,
  ]);

  const onPrintPhieu = useRefFunc(async (item) => {
    if (item.ma == "P1182") {
      refBienBanHuyThuoc.current &&
        refBienBanHuyThuoc.current.show({}, async (filterData) => {
          try {
            showLoading();
            const { finalFile, dsPhieu } = await getFilePhieuIn({
              listPhieus: [item],
              denThoiGian: filterData?.denThoiGian,
              tuThoiGian: filterData?.tuThoiGian,
              showError: true,
              dsKhoId: filterData?.dsKhoId,
              dsDichVuId: filterData?.dsDichVuId,
            });
            if (filterData?.xuatExcel && dsPhieu?.length) {
              const fileUrl = dsPhieu[0]?.file?.doc;
              if (fileUrl) {
                fileUtils
                  .getFromUrl({ url: fileUtils.absoluteFileUrl(fileUrl) })
                  .then((s) => {
                    const blob = new Blob([new Uint8Array(s)], {
                      type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                    });
                    fileUtils.downloadBlob(blob, `${fileUrl}`); //or any other extension
                  });
              }
            } else {
              if ((dsPhieu || []).every((x) => x?.loaiIn == 20)) {
                openInNewTab(finalFile);
              } else {
                printProvider.printPdf(dsPhieu);
              }
            }
          } catch (error) {
            console.log("error", error);
          } finally {
            hideLoading();
          }
        });
    } else if (item.loaiBieuMau == LOAI_BIEU_MAU.BIEU_MAU_CHINH_SUA) {
      let mhParams = {};
      //kiểm tra phiếu ký số
      if (checkIsPhieuKySo(item)) {
        //nếu là ký số thì lấy các thông tin về lịch sử ký + tên chân ký + báo cáo id
        mhParams = {
          maManHinh: item.maManHinh,
          maViTri: item.maViTri,
          kySo: true,
          maPhieuKy: item.ma,
        };
      }

      const lichSuKyId = item?.dsSoPhieu?.length
        ? item?.dsSoPhieu[0].lichSuKyId
        : "";

      //Phiếu bàn giao thuốc cho người bệnh
      if (item.ma == "P1183") {
        refModalChonPhieuBanGiaoThuocNB.current &&
          refModalChonPhieuBanGiaoThuocNB.current.show({}, (filterData) => {
            showFileEditor({
              phieu: item,
              id: filterData?.id,
              nbDotDieuTriId: filterData?.nbDotDieuTriId,
              ma: item.ma,
              maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
                ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
                : "",
              dsKhoaChiDinhId: filterData?.dsKhoaChiDinhId,
              ngayThucHien: filterData?.ngayThucHien,
              mhParams,
            });
          });
      } else {
        showFileEditor({
          phieu: item,
          ma: item.ma,
          maBaoCao: MA_BIEU_MAU_EDITOR[item.ma]
            ? MA_BIEU_MAU_EDITOR[item.ma].maBaoCao
            : "",
          mhParams,
          lichSuKyId,
        });
      }
    } else {
      try {
        showLoading();
        const { finalFile, dsPhieu } = await getFilePhieuIn({
          listPhieus: [item],
          showError: true,
        });
        if ((dsPhieu || []).every((x) => x?.loaiIn == 20)) {
          openInNewTab(finalFile);
        } else {
          printProvider.printPdf(dsPhieu);
        }
      } catch (error) {
        console.log("error", error);
      } finally {
        hideLoading();
      }
    }
  });

  const contentPrint = useMemo(() => {
    return (
      <Menu
        items={(state?.listPhieu || []).map((item, index) => ({
          key: index,
          label: (
            <a href={() => false} onClick={() => onPrintPhieu(item)}>
              {item.ten || item.tenBaoCao}
            </a>
          ),
        }))}
      />
    );
  }, [state.listPhieu]);

  const handleClick = (action) => async () => {
    const data = state.dsSelected.map((o) => ({ id: o.id }));
    showLoading();
    try {
      switch (action) {
        case "duyetDuoc":
          await duyetDuyetDuoc(data);
          break;
        case "huyDuyetDuoc":
          await huyDuyetDuoc(data);
          break;
        case "xacNhanNb":
          await xacNhanNb(data);
          break;
        case "huyXacNhanNb":
          await huyXacNhanNb(data);
          break;
        case "duyetPhaChe":
          await duyetPhaChe(data);
          break;
        case "huyDuyetPhaChe":
          await huyDuyetPhaChe(data);
          break;
        case "giaoPhaChe":
          await giaoPhaChe(data);
          break;
        case "huyGiaoPhaChe":
          await huyGiaoPhaChe(data);
          break;
        default:
          break;
      }
      await onChangeInputSearch({});
      refBangDanhSach.current && refBangDanhSach.current.resetSelected();
      _setState({});
    } catch (error) {
    } finally {
      hideLoading();
    }
  };

  const renderActionButton = () => {
    return (
      <div className="header-action">
        {checkRole([ROLES["PHA_CHE"].DUYET_DUOC_THEO_DON]) &&
          visibleDuyetDuoc && (
            <Button type="default" onClick={handleClick("duyetDuoc")}>
              {t("phaCheThuoc.duyetDuoc")}
            </Button>
          )}
        {visibleHuyDuyetDuoc &&
          checkRole([ROLES["PHA_CHE"].HUY_DUYET_DUOC_THEO_DON]) && (
            <Button type="default" onClick={handleClick("huyDuyetDuoc")}>
              {t("phaCheThuoc.huyDuyetDuoc")}
            </Button>
          )}
        {checkRole([ROLES["PHA_CHE"].XAC_NHAN_NB_THEO_DON]) &&
          visibleXacNhanNb && (
            <Button type="default" onClick={handleClick("xacNhanNb")}>
              {t("phaCheThuoc.xacNhanNb")}
            </Button>
          )}
        {visibleHuyXacNhanNb &&
          checkRole([ROLES["PHA_CHE"].HUY_XAC_NHAN_NB_THEO_DON]) && (
            <Button type="default" onClick={handleClick("huyXacNhanNb")}>
              {t("phaCheThuoc.huyXacNhanNb")}
            </Button>
          )}
        {checkRole([ROLES["PHA_CHE"].DUYET_PHA_CHE_THUOC]) &&
          visibleDuyetPhaChe && (
            <Button type="default" onClick={handleClick("duyetPhaChe")}>
              {t("phaCheThuoc.phaChe")}
            </Button>
          )}
        {visibleHuyDuyetPhaChe &&
          checkRole([ROLES["PHA_CHE"].HUY_PHA_CHE_THUOC]) && (
            <Button type="default" onClick={handleClick("huyDuyetPhaChe")}>
              {t("phaCheThuoc.huyPhaChe")}
            </Button>
          )}
        {checkRole([ROLES["PHA_CHE"].GIAO_PHA_CHE_THUOC]) &&
          visibleGiaoPhaChe && (
            <Button type="default" onClick={handleClick("giaoPhaChe")}>
              {t("phaCheThuoc.giaoPhaChe")}
            </Button>
          )}
        {checkRole([ROLES["PHA_CHE"].HUY_GIAO_PHA_CHE_THUOC]) &&
          visibleHuyGiaoPhaChe && (
            <Button type="default" onClick={handleClick("huyGiaoPhaChe")}>
              {t("phaCheThuoc.huyGiaoPhaChe")}
            </Button>
          )}

        <Dropdown
          overlayClassName="dropdown-pha-che-thuoc"
          overlay={contentPrint}
          trigger={["click"]}
        >
          <Button className="ic-print" rightIcon={<SVG.IcPrint />}>
            {t("common.inGiayTo")}
          </Button>
        </Dropdown>
      </div>
    );
  };

  return (
    <MainPage
      breadcrumb={[
        { title: t("phaCheThuoc.phaCheThuoc"), link: "/pha-che-thuoc" },
        {
          title: t("phaCheThuoc.danhSachPhieuPhaCheThuocTheoDon"),
          link: "/pha-che-thuoc/danh-sach-pha-che-thuoc",
        },
      ]}
      title={t("phaCheThuoc.danhSachPhieuPhaCheThuocTheoDon")}
      titleRight={renderActionButton()}
    >
      <div className="wrapper-container">
        <BoLocTimKiem />
        <BangDanhSach ref={refBangDanhSach} onSelect={onSelect} />
      </div>

      <ModalChonPhieuBanGiaoThuocNB ref={refModalChonPhieuBanGiaoThuocNB} />
      <ModalBienBanHuyThuoc ref={refBienBanHuyThuoc} />
    </MainPage>
  );
};

export default DsPhaCheThuocTheoDon;
