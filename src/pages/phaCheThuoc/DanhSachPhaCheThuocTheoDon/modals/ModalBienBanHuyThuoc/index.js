import React, {
  forwardRef,
  useImperativeHandle,
  useRef,
  useState,
  useEffect,
} from "react";
import { useTranslation } from "react-i18next";
import { Button, ModalTemplate, Select, DatePicker } from "components";
import { HOTKEY } from "constants/index";
import { Main } from "./styled";
import { Checkbox, Form } from "antd";
import { SVG } from "assets";
import moment from "moment";
import { useStore } from "hooks";
import { useDispatch } from "react-redux";

const ModalChonPhieuBanGiaoThuocNB = (props, ref) => {
  const { t } = useTranslation();
  const [state, _setState] = useState({
    show: false,
    data: {},
    dsSoPhieu: [],
  });
  const setState = (data = {}) => {
    _setState((state) => ({ ...state, ...data }));
  };

  const refModal = useRef(null);
  const refCallback = useRef(null);
  const [form] = Form.useForm();
  const listKhoUser = useStore("kho.listKhoUser", []);
  const listAllData = useStore("danhMucThuoc.listAllData", []);
  const {
    kho: { getTheoTaiKhoan: getKhoTheoTaiKhoan },
    danhMucThuoc: { onSearchAll: onSearchAllThuoc },
  } = useDispatch();

  useImperativeHandle(ref, () => ({
    show: (data = {}, callBack) => {
      setState({ show: true, data });
      form.setFieldsValue({
        tuThoiGian: moment().startOf("days"),
        denThoiGian: moment().endOf("days"),
      });
      refCallback.current = callBack;
    },
  }));
  const onOk = (isOk) => () => {
    if (isOk) {
      form.submit();
    } else {
      setState({ show: false });
    }
  };
  const hotKeys = [
    {
      keyCode: HOTKEY.ESC,
      onEvent: () => {
        onOk(false)();
      },
    },
    {
      keyCode: HOTKEY.F4,
      onEvent: () => {
        onOk(true)();
      },
    },
  ];

  useEffect(() => {
    if (state.show) {
      refModal.current.show();
      getKhoTheoTaiKhoan({ page: "", size: "", active: true });
      onSearchAllThuoc({ page: "", size: "", loaiDichVu: 90 });
    } else {
      refModal.current.hide();
    }
  }, [state.show]);

  const onHandleSubmit = () => {
    form
      .validateFields()
      .then((values) => {
        const data = {
          tuThoiGian: values?.tuThoiGian
            ? moment(values?.tuThoiGian).format("YYYY-MM-DD HH:mm:ss")
            : null,
          denThoiGian: values?.denThoiGian
            ? moment(values?.denThoiGian).format("YYYY-MM-DD HH:mm:ss")
            : null,
          dsKhoId: values?.dsKhoId,
          dsDichVuId: values?.dsDichVuId,
          xuatExcel: values?.xuatExcel
        };

        refCallback.current && refCallback.current(data);
        setState({ show: false });
      })
      .catch((e) => {
        console.log(e);
      });
  };

  return (
    <ModalTemplate
      ref={refModal}
      hotKeys={hotKeys}
      onCancel={onOk(false)}
      title={t("quanLyNoiTru.chonTieuChi")}
      width={650}
      actionLeft={
        <Button.QuayLai onClick={onOk(false)}>
          {t("common.quayLai")} [ESC]
        </Button.QuayLai>
      }
      actionRight={
        <Button
          type="primary"
          minWidth={100}
          onClick={onOk(true)}
          rightIcon={<SVG.IcSave />}
        >
          <span> {t("common.dongY")}</span>
        </Button>
      }
    >
      <Main>
        <Form
          form={form}
          layout="vertical"
          style={{ width: "100%" }}
          className="form-custom"
          onFinish={onHandleSubmit}
        >
          <Form.Item
            label={t("common.tuThoiGian")}
            name="tuThoiGian"
            style={{ width: "100%" }}
            rules={[
              {
                required: true,
                message: t("common.vuiLongChonNgay"),
              },
            ]}
          >
            <DatePicker showTime></DatePicker>
          </Form.Item>
          <Form.Item
            label={t("common.denThoiGian")}
            name="denThoiGian"
            style={{ width: "100%" }}
            rules={[
              {
                required: true,
                message: t("common.vuiLongChonNgay"),
              },
            ]}
          >
            <DatePicker showTime></DatePicker>
          </Form.Item>
          <Form.Item
            label={t("baoCao.kho")}
            name="dsKhoId"
            style={{ width: "100%" }}
          >
            <Select
              className="select"
              placeholder={t("baoCao.kho")}
              data={listKhoUser}
              mode="multiple"
            />
          </Form.Item>
          <Form.Item
            label={t("common.dichVu")}
            name="dsDichVuId"
            style={{ width: "100%" }}
          >
            <Select
              className="select"
              placeholder={t("common.dichVu")}
              data={listAllData.map((item) => ({
                id: item.id,
                ten: `${item.ma} - ${item.ten}`,
              }))}
              mode="multiple"
            />
          </Form.Item>

          <Form.Item label=" " name="xuatExcel" valuePropName="checked">
            <Checkbox>{t("common.xuatExcel")}</Checkbox>
          </Form.Item>
        </Form>
      </Main>
    </ModalTemplate>
  );
};
export default forwardRef(ModalChonPhieuBanGiaoThuocNB);
