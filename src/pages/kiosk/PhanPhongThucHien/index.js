import React, { useState, useCallback, useRef, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Input, Spin } from "antd";
import { toSafePromise } from "lib-utils";
import { useCache, useFillMaHoSo, useStore } from "hooks";
import { isObject } from "utils/index";
import { SVG } from "assets";
import { Card } from "components";
import goiSoProvider from "data-access/goi-so-provider";
import { KiosWrapper } from "../components";
import { MainWrapper } from "./styled";
import { CACHE_KEY } from "constants/index";
import arrowImg from "assets/images/kiosk/arrow.png";
import printProvider from "data-access/print-provider";
import nbThanhToanProvider from "data-access/nb-thanh-toan-provider";
import { useDispatch } from "react-redux";
import ModalConfigKhuVuc from "../components/ModalConfigKhuVuc";
import moment from "moment";

const RESET_TIMEOUT = 8000;
const QR_PATTERNS = {
  NB_CODE: /^NB.*\$$/,
};

const ERROR_MESSAGES = {
  KHU_VUC_NOT_FOUND: "kiosk.khongTimThayKhuVuc",
  PATIENT_NOT_FOUND: "kiosk.khongTimThayThongTinNguoiBenh",
  INSUFFICIENT_FUNDS:
    "kiosk.nguoiBenhConThieuTamUngVuiLongQuetMaQrTrenPhieuDeNapTienVaTiepTucThanhToan",
  PATIENT_HAVE_NOT_DICH_VU: "kiosk.nguoiBenhKhongCoDichVuThucHien",
  GENERAL_ERROR: "kiosk.coLoiXayRaVuiLongThuLai",
  HET_DINH_MUC_THUC_HIEN_DICH_VU:
    "kiosk.hetDinhMucThucHienDichVuVuiLongQuayVePhongKham",
};

const PhanPhongThucHien = React.memo(() => {
  const { t } = useTranslation();
  const refModalConfigKhuVuc = useRef(null);
  const {
    khuVuc: { getListAllKhuVuc },
    choTiepDonDV: { inPhieuStt },
  } = useDispatch();
  const listAllKhuVuc = useStore("khuVuc.listAllKhuVuc", []);

  const [cacheConfigKhuVuc, setCacheConfigKhuVuc] = useCache(
    "",
    CACHE_KEY.CONFIG_KHU_VUC,
    null,
    false,
    false
  );
  const [input, setInput] = useState("");
  const [state, _setState] = useState({
    error: "",
    loading: false,
    success: false,
    stt: null,
  });
  const { formatMaHoSo, testMaHoSo } = useFillMaHoSo();

  const timeoutRef = useRef(null);
  const inputRef = useRef(null);

  const setState = useCallback((data) => {
    _setState((prev) => ({
      ...prev,
      ...data,
    }));
  }, []);

  useEffect(() => {
    getListAllKhuVuc(
      {
        active: true,
        page: "",
        size: "",
      },
      { saveCache: false }
    );
  }, []);

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (!state.loading && !state.success && !state.error && inputRef.current) {
      inputRef.current.focus();
    }
  }, [state.loading, state.success, state.error]);

  const clearResetTimeout = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  const scheduleReset = useCallback(() => {
    clearResetTimeout();
    timeoutRef.current = setTimeout(() => {
      setState({ error: null, success: false });
    }, RESET_TIMEOUT);
  }, [clearResetTimeout, setState]);

  const parseQRCode = useCallback((value) => {
    if (!value) return null;

    const array = value.split("|");

    if (testMaHoSo(value)) {
      return { maHoSo: formatMaHoSo(value) };
    } else if (QR_PATTERNS.NB_CODE.test(value)) {
      return { maNb: array[4] };
    } else if (value.endsWith("}")) {
      return { maNb: JSON.parse(value)?.maNb };
    } else if (array.length === 7 || array.length === 11) {
      return { maSoGiayToTuyThan: array[0] };
    } else {
      return { maSoGiayToTuyThan: array[0] };
    }
  }, []);

  const handlePrintQr = useCallback(async (thanhToanId) => {
    try {
      const [, thanhToanQrData] = await toSafePromise(
        nbThanhToanProvider.inPhieuHuongDanThanhToanQr({
          thanhToanId,
        })
      );
      if (thanhToanQrData) {
        await printProvider.printPdf({
          ...thanhToanQrData,
          silentPrint: true,
        });
      }
    } catch (error) {
      console.error("HandlePrintQr error:", error);
    }
  }, []);

  const processGetNbDvKyThuat = useCallback(
    async (params) => {
      setState({ loading: true, error: null });

      try {
        if (!cacheConfigKhuVuc?.[0]) {
          setState({
            loading: false,
            error: t(ERROR_MESSAGES.KHU_VUC_NOT_FOUND),
          });
          scheduleReset();
          return;
        }
        const [searchError, searchResult] = await toSafePromise(
          goiSoProvider.getNbDvKyThuat({
            ...params,
            doiTuongKcb: 1,
          })
        );

        if (searchError || !searchResult?.data) {
          setState({
            loading: false,
            error: t(ERROR_MESSAGES.PATIENT_NOT_FOUND),
          });
          scheduleReset();
          return;
        }
        if (searchResult?.data?.qrThanhToan?.length > 0) {
          await handlePrintQr(searchResult?.data?.qrThanhToan[0]?.id);
          setState({
            loading: false,
            error: t(ERROR_MESSAGES.INSUFFICIENT_FUNDS),
          });
          scheduleReset();
          return;
        }
        let arrayDichVu = [];

        searchResult.data.dsDichVu?.forEach((item) => {
          if (
            item.dsKhuVucThucHienId?.[0] === cacheConfigKhuVuc?.[0] &&
            moment(item.thoiGianThucHien).isBefore(moment())
          ) {
            arrayDichVu.push({
              id: item.id,
              ten: item.tenDichVu,
            });
          }
        });

        if (arrayDichVu.length === 0) {
          setState({
            loading: false,
            error: t(ERROR_MESSAGES.PATIENT_HAVE_NOT_DICH_VU),
          });
          scheduleReset();
          return;
        }

        const [phanPhongError, phanPhongResult] = await toSafePromise(
          goiSoProvider.phanPhongThucHien(
            arrayDichVu.map((dv) => ({
              id: dv.id,
            }))
          )
        );

        if (phanPhongError || phanPhongResult?.code !== 0) {
          setState({
            loading: false,
            error: phanPhongResult.message || t(ERROR_MESSAGES.GENERAL_ERROR),
          });
          scheduleReset();
          return;
        }
        const { dsId, noDv } = phanPhongResult?.data?.reduce(
          (acc, item) => {
            if (item.phongThucHienId) {
              acc.dsId.push(item.id);
            } else {
              const dichVu = arrayDichVu.find((dv) => dv.id === item.id);

              acc.noDv.push(dichVu);
            }
            return acc;
          },
          { dsId: [], noDv: [] }
        );

        if (dsId.length === 0) {
          setState({
            loading: false,
            error: t(ERROR_MESSAGES.HET_DINH_MUC_THUC_HIEN_DICH_VU),
          });
          scheduleReset();
          return;
        }

        const inPhieuSttResult = await inPhieuStt(dsId);

        if (!inPhieuSttResult) {
          setState({
            loading: false,
            error: t(ERROR_MESSAGES.GENERAL_ERROR),
          });
          scheduleReset();
          return;
        }

        setState({
          loading: false,
          success: true,
          noDv,
        });

        scheduleReset();
      } catch (error) {
        console.error("PhanPhongThucHien processing error:", error);
        setState({
          loading: false,
          error: t(ERROR_MESSAGES.GENERAL_ERROR),
        });
        scheduleReset();
      }
    },
    [setState, scheduleReset, handlePrintQr, t, cacheConfigKhuVuc]
  );

  const onChange = useCallback((e) => {
    setInput(e.target.value.trim());
  }, []);

  const onKeyDown = useCallback(
    async (e) => {
      const value = e.target.value || input;

      if (e.key === "Enter" || e.key === "Tab") {
        if (!value || state.loading) return;

        const params = parseQRCode(value);
        if (params && isObject(params, true)) {
          setInput("");
          await processGetNbDvKyThuat(params);
        }
      }
    },
    [input, state.loading, parseQRCode, processGetNbDvKyThuat]
  );

  const baseContent = () => {
    return (
      <>
        <h1 id="main-title">{t("kiosk.quetMaLaySoThuTuThucHienDichVu")}</h1>
        <p id="instruction-text">
          {t("kiosk.vuiLongQuetMaQrCodeTrenCccdAppVneidHoacMaQrCodeDaDuocCap")}
        </p>
        <div className="icon-wrapper">
          <SVG.IcCCCD />
          <SVG.IcVneid />
          <SVG.IcPhieuQr />
        </div>
        <div className="input-wrapper">
          <div className="input-search">
            <Input
              ref={inputRef}
              placeholder={t("kiosk.qrCccdQrMaNb")}
              onKeyDown={onKeyDown}
              onChange={onChange}
              value={input}
              disabled={state.loading}
              autoComplete="off"
              autoFocus={!state.loading}
              aria-label={t("kiosk.qrCccdQrMaNb")}
              aria-describedby="instruction-text"
              aria-invalid={!!state.error}
              suffix={state.loading ? <Spin size="small" /> : <SVG.IcQrCode />}
            />
          </div>
          {state.loading && (
            <div
              className="loading-text"
              role="status"
              aria-live="polite"
              aria-label={t("kiosk.dangXuLyVuiLongCho")}
            >
              {t("kiosk.dangXuLyVuiLongCho")}
            </div>
          )}
        </div>
      </>
    );
  };

  const successContent = () => {
    return (
      <div className="success-content" role="alert" aria-live="polite">
        <SVG.IcCheckCircleGreen aria-hidden="true" />
        <h1 id="success-title">
          {t("kiosk.inSoThuTuThanhCongVuiLongNhanSoThuTuThucHienDichVu")}
        </h1>

        {Array.isArray(state.noDv) && state.noDv.length > 0 && (
          <div className="no-dv-wrapper">
            <div className="no-dv-title">
              {t("kiosk.cacDichVuKhongDuocInStt")}
            </div>
            <ul className="no-dv-list">
              {state.noDv.map((item, index) => (
                <li key={index} className="no-dv-item">
                  {item?.ten}
                </li>
              ))}
            </ul>
          </div>
        )}

        <div className="footer">
          <div className="footer-text">
            {t("kiosk.xinMoiLayPhieuDaDuocNhaRa")}
          </div>
          <div className="image">
            <img
              src={arrowImg}
              alt={t("kiosk.huongDanLayPhieu")}
              aria-hidden="true"
            />
          </div>
        </div>
      </div>
    );
  };

  const errorContent = () => {
    return (
      <div className="error-content" role="alert" aria-live="assertive">
        <SVG.IcCloseCircle
          color={"var(--color-red-primary)"}
          aria-hidden="true"
        />
        <h1
          id="error-title"
          dangerouslySetInnerHTML={{
            __html: t("kiosk.phanPhongThucHienKhongThanhCongMessage", {
              message: state.error,
            }),
          }}
        />
      </div>
    );
  };

  const onLogoClick = () => {
    refModalConfigKhuVuc.current &&
      refModalConfigKhuVuc.current.show(
        { cacheConfigKhuVuc, listAllKhuVuc },
        setCacheConfigKhuVuc
      );
  };

  return (
    <KiosWrapper onLogoClick={onLogoClick}>
      <MainWrapper>
        {!state.success && !state.error && (
          <header className="top">
            <div className="header">
              <h1 className="title">{t("kiosk.kioskPhanPhongThucHien")}</h1>
              <p className="sub-header">
                {cacheConfigKhuVuc?.[0]
                  ? listAllKhuVuc.find(
                      (khuVuc) => khuVuc.id === cacheConfigKhuVuc[0]
                    )?.ten
                  : null}
              </p>
            </div>
          </header>
        )}
        <main className={"content"} role="main">
          <div className="header-content" role="banner">
            <SVG.IcThuTamUng aria-hidden="true" />
            <span>{t("kiosk.inSoThuTuTuDong").toUpperCase()}</span>
          </div>
          <Card
            className="card-content"
            role="region"
            aria-labelledby={
              state.success
                ? "success-title"
                : state.error
                ? "error-title"
                : "main-title"
            }
          >
            {state.success
              ? successContent()
              : state.error
              ? errorContent()
              : baseContent()}
          </Card>
        </main>
      </MainWrapper>
      <ModalConfigKhuVuc ref={refModalConfigKhuVuc}></ModalConfigKhuVuc>
    </KiosWrapper>
  );
});

export default PhanPhongThucHien;
