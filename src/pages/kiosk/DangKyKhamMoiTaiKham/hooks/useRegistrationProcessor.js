import { useCallback, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { toSafePromise } from "lib-utils";

import { useQueryAll, useThietLap } from "hooks";

import { isArray, parseListConfig } from "utils/index";
import goiSoProvider from "data-access/goi-so-provider";
import nbDotDieuTriProvider from "data-access/nb-dot-dieu-tri-provider";
import { ERROR_MESSAGES } from "../constants";
import { isUuTien, parseBodyHenKham, getThuValue } from "../utils";
import { query } from "redux-store/stores";
import { LOAI_PHONG } from "constants";
import nbDvKhamProvider from "data-access/nb-dv-kham-provider";
import printProvider from "data-access/print-provider";
import { LOAI_DICH_VU, THIET_LAP_CHUNG } from "constants/index";

const useRegistrationProcessor = (khuVuc, setState, scheduleReset) => {
  const { t } = useTranslation();

  const { data: listAllPhong } = useQueryAll(
    query.phong.queryAllPhong({
      params: {
        dsLoaiPhong: [LOAI_PHONG.PHONG_KHAM],
      },
    })
  );
  const { data: listAllLoaiDoiTuong } = useQueryAll(
    query.loaiDoiTuong.queryAllLoaiDoiTuong
  );
  const [dataMA_LOAI_DT_DUYET_BH_TRUOC_KHAM] = useThietLap(
    THIET_LAP_CHUNG.MA_LOAI_DT_DUYET_BH_TRUOC_KHAM
  );

  const commonParams = useMemo(
    () => ({
      khuVucId: khuVuc?.id,
      doiTuong: 1,
      loaiGiayTo: 1,
    }),
    [khuVuc?.id]
  );

  const handleSuccessResult = useCallback(
    (stt, uuTien) => {
      setState({
        loading: false,
        success: true,
        stt,
        uuTien,
      });
      scheduleReset();
    },
    [setState, scheduleReset]
  );

  const handleErrorResult = useCallback(
    (error) => {
      const errorMessage = error?.message || t(ERROR_MESSAGES.GENERAL_ERROR);
      setState({
        loading: false,
        error: errorMessage,
      });
      scheduleReset();
      return {
        success: false,
        error: errorMessage,
      };
    },
    [setState, scheduleReset, t]
  );

  const processGetNumber = useCallback(
    async (params) => {
      try {
        const requestParams = {
          ...params,
          ...commonParams,
          uuTien: isBoolean(params.uuTien)
            ? params.uuTien
            : isUuTien(params.ngaySinh),
        };

        const infoGetNumber = await goiSoProvider.laySo(requestParams);

        // In phiếu STT
        if (infoGetNumber?.data?.id) {
          goiSoProvider.inPhieuSTT({ id: infoGetNumber?.data?.id });
        }

        return {
          success: true,
          data: infoGetNumber?.data,
          stt: infoGetNumber?.data?.stt,
          uuTien: infoGetNumber?.data?.uuTien,
        };
      } catch (error) {
        console.error("Error getting number:", error);
        return {
          success: false,
          error: error?.message || t(ERROR_MESSAGES.GENERAL_ERROR),
        };
      }
    },
    [commonParams, t]
  );

  const handleGetNumberResult = useCallback(
    (result) => {
      if (result.success) {
        handleSuccessResult(result.stt, result.uuTien);
      } else {
        setState({
          loading: false,
          error: result.error,
        });
        scheduleReset();
      }
      return result;
    },
    [handleSuccessResult, setState, scheduleReset]
  );

  const processNewRegistration = useCallback(
    async (values) => {
      try {
        setState({ loading: true });
        const result = await processGetNumber(values);
        return handleGetNumberResult(result);
      } catch (error) {
        console.error("Error in new registration:", error);
        return handleErrorResult(error);
      }
    },
    [processGetNumber, handleGetNumberResult, handleErrorResult]
  );

  const showRegistrationForm = useCallback(
    (otherInfo) => {
      setState({
        loading: false,
        otherInfo,
        showForm: true,
      });
      return { success: false, showForm: true };
    },
    [setState]
  );

  const createGetNumberParams = useCallback((patientData, otherInfo) => {
    const {
      tenNb,
      ngaySinh,
      gioiTinh,
      soDienThoai,
      soNha,
      xaPhuongId,
      tinhThanhPhoId,
      quocGiaId,
      maSoGiayToTuyThan,
    } = patientData;

    return {
      tenNb,
      ngaySinh,
      soDienThoai,
      gioiTinh,
      soNha,
      xaPhuongId,
      tinhThanhPhoId,
      quocGiaId,
      maSoGiayToTuyThan,
      ngayCapGiayToTuyThan: otherInfo?.ngayCapGiayToTuyThan,
      loaiGiayTo: 1,
    };
  }, []);

  const processRegistration = useCallback(
    async ({ otherInfo, ...params }) => {
      setState({ loading: true, error: null });

      try {
        // Tìm kiếm thông tin người bệnh
        const [searchError, searchResult] = await toSafePromise(
          nbDotDieuTriProvider.searchNBDotDieuTriTongHop({
            ...params,
            page: 0,
            size: 1,
          })
        );

        // Nếu không tìm thấy hoặc có lỗi, chuyển sang form đăng ký
        if (searchError || !isArray(searchResult?.data, true)) {
          return showRegistrationForm(otherInfo);
        }

        // Kiểm tra lịch hẹn khám
        const [errLichHen, lichHenKham] = await toSafePromise(
          goiSoProvider.getLichHenKham({ maNb: searchResult.data[0]?.maNb })
        );

        // Nếu không có lịch hẹn
        if (errLichHen || !isArray(lichHenKham?.data, true)) {
          // Lấy số thứ tự cho NB
          const patientData = searchResult.data[0];
          const getNumberParams = createGetNumberParams(patientData, otherInfo);

          const result = await processGetNumber(getNumberParams);
          return handleGetNumberResult(result);
        }

        // Xử lý hẹn khám
        const thongTinHenKham = lichHenKham.data[0];
        let maLoaiDoiTuong;
        const listMaDoiTuong = parseListConfig(
          dataMA_LOAI_DT_DUYET_BH_TRUOC_KHAM
        );
        if (isArray(listMaDoiTuong, 1, 1)) {
          maLoaiDoiTuong = listMaDoiTuong[0];
        } else if (isArray(listMaDoiTuong, 1)) {
          let today = getThuValue();
          maLoaiDoiTuong = listAllLoaiDoiTuong.find(
            (item) =>
              item.dsNgayHoatDong?.includes(today) &&
              listMaDoiTuong.includes(item.ma)
          )?.ma;
        }
        const body = parseBodyHenKham({
          data: thongTinHenKham,
          listAllPhong,
          dsDichVu: lichHenKham.data,
          maLoaiDoiTuong,
        });

        const [henKhamError, henKhamResult] = await toSafePromise(
          goiSoProvider.postNbHenKham(body)
        );

        if (henKhamError) {
          // Nếu lỗi hẹn khám, fallback về lấy số thông thường
          const patientData = searchResult.data[0];
          const getNumberParams = createGetNumberParams(patientData, otherInfo);

          const result = await processGetNumber(getNumberParams);
          return handleGetNumberResult(result);
        }

        // Thành công với hẹn khám
        setState({
          loading: false,
          success: true,
        });
        scheduleReset();

        // In phiếu chỉ định khám
        if (henKhamResult?.data?.id) {
          const [_, phieuChiDinhResult] = await toSafePromise(
            nbDvKhamProvider.getPhieuChiDinh({
              nbDotDieuTriId: henKhamResult?.data?.id,
              dsChiDinhTuLoaiDichVu: LOAI_DICH_VU.TIEP_DON,
              dsNbDichVuId: henKhamResult?.data?.dsDichVu?.map(
                (item) => item.id
              ),
            })
          );
          printProvider.printPdf(phieuChiDinhResult?.data);
        }

        return { success: true };
      } catch (error) {
        console.error("Error in registration processing:", error);
        return handleErrorResult(error);
      }
    },
    [
      setState,
      showRegistrationForm,
      createGetNumberParams,
      processGetNumber,
      handleGetNumberResult,
      scheduleReset,
      handleErrorResult,
      listAllPhong,
      dataMA_LOAI_DT_DUYET_BH_TRUOC_KHAM,
      listAllLoaiDoiTuong,
    ]
  );

  return useMemo(
    () => ({
      processRegistration,
      processNewRegistration,
      processGetNumber,
    }),
    [processRegistration, processNewRegistration, processGetNumber]
  );
};

export default useRegistrationProcessor;
