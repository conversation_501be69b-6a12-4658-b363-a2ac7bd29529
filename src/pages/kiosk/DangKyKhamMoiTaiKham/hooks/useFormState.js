import { useState, useCallback, useEffect } from "react";
import { isObject } from "utils/index";
import { isUuTien } from "../utils";

const useFormState = (otherInfo, form, refAddressFull) => {
  const [state, setState] = useState({
    ngaySinh: "",
    validate: 0,
    checkNgaySinh: false,
  });

  const updateState = useCallback((data) => {
    setState((prevState) => ({
      ...prevState,
      ...data,
    }));
  }, []);

  const onChangeNgaySinh = useCallback(
    (value) => {
      updateState({ ngaySinh: value, validate: 0 });
      form.setFields([{ name: "ngaySinh", value }]);
      form.setFieldsValue({
        uuTien: value?.date ? isUuTien(value?.date) : false,
      });
    },
    [updateState, form]
  );

  const onChangeAddress = useCallback(
    (data) => {
      if (data) {
        let obj = {
          xaPhuongId: data.xaPhuongId ?? data?.id,
          tinhThanhPhoId: data.tinhThanhPhoId || data?.tinhThanhPho?.id,
          quocGiaId: data.quocGiaId || data?.tinhThanhPho?.quocGia?.id,
        };
        form.setFieldsValue(obj);
        setState(obj);
      }
    },
    [form]
  );

  const onValidate = useCallback(
    (value) => {
      updateState({ validate: value });
    },
    [updateState]
  );

  useEffect(() => {
    if (isObject(otherInfo)) {
      const { ngaySinh, ...rest } = otherInfo;
      form.setFieldsValue({
        ...otherInfo,
        uuTien: ngaySinh?.date ? isUuTien(ngaySinh?.date) : false,
      });
      updateState({ ngaySinh });

      if (rest.diaChi && refAddressFull.current) {
        refAddressFull.current.setAddress(rest.diaChi);
      }
    }
  }, [otherInfo, form, refAddressFull, updateState]);

  return {
    state,
    updateState,
    onChangeNgaySinh,
    onChangeAddress,
    onValidate,
  };
};

export default useFormState;
