import React, {
  useMemo,
  forwardRef,
  useImperativeHandle,
  useRef,
  useCallback,
  useEffect,
} from "react";
import { Form, Input, Row, Col, Radio } from "antd";
import { useTranslation } from "react-i18next";

import { useEnum, useThietLap } from "hooks";

import { DOBInput, AddressFull, Checkbox } from "components";
import { ENUM, GIOI_TINH, THIET_LAP_CHUNG } from "constants/index";
import useFormState from "./hooks/useFormState";
import { chuyenDoiTenNb } from "utils/index";
import { checkRole } from "lib-utils/role-utils";

const FormDangKyKham = React.memo(
  forwardRef(function FormDangKyKham({ otherInfo, form, onFinish }, ref) {
    const { t } = useTranslation();
    const refAddressFull = useRef(null);

    const { state, onChangeNgaySinh, onChangeAddress, onValidate } =
      useFormState(otherInfo, form, refAddressFull);
    const [dataMAP_HO_TEN_UNICODE, finish] = useThietLap(
      THIET_LAP_CHUNG.MAP_HO_TEN_UNICODE
    );

    const [listGioiTinh] = useEnum(ENUM.GIOI_TINH);

    const listGioiTinhMemo = useMemo(() => {
      return listGioiTinh.filter((item) => item.id !== 3);
    }, [listGioiTinh]);

    useImperativeHandle(ref, () => ({
      submit: () => {
        form.submit();
      },
    }));

    const onErrorAddress = useCallback(() => {
      form.setFields([
        {
          name: "diaChi",
          errors: [t("tiepDon.diaChiHanhChinhKhongHopLe")],
        },
      ]);
    }, [form, t]);

    const validateNgaySinh = useCallback(
      (_, value) => {
        if (value?.str && state.validate && state.validate !== 0) {
          return Promise.reject(new Error(t("tiepDon.ngaySinhSaiDinhDang")));
        }
        return Promise.resolve();
      },
      [state.validate, t]
    );

    useEffect(() => {
      if (state.validate !== undefined && state.ngaySinh) {
        setTimeout(() => {
          form.validateFields(["ngaySinh"]);
        }, 0);
      }
    }, [state.validate, state.ngaySinh, form]);

    const handleDOBBlur = useCallback(
      (e, nofi) => {
        onChangeNgaySinh(e);

        onValidate(nofi);

        setTimeout(() => {
          form.validateFields(["ngaySinh"]);
        }, 0);
      },
      [onChangeNgaySinh, onValidate, form]
    );

    const handleDOBChange = useCallback(
      (e) => {
        onChangeNgaySinh(e);

        setTimeout(() => {
          form.validateFields(["ngaySinh"]);
        }, 0);
      },
      [onChangeNgaySinh, form]
    );

    const onChangeAdrressText = useCallback(
      (e) => {
        form.setFields([
          {
            name: "diaChi",
            value: e,
            errors: e && e.trim() ? [] : undefined,
          },
        ]);
      },
      [form]
    );

    const selectAdress = useCallback(
      (data) => {
        onChangeAddress(data);
      },
      [onChangeAddress]
    );

    const mapHoTenUnicode = useMemo(() => {
      if (finish) {
        try {
          return Object.entries(JSON.parse(dataMAP_HO_TEN_UNICODE)).map(
            (item) => ({ key: item[0], value: item[1] })
          );
        } catch (error) {
          return [];
        }
      }
      return [];
    }, [dataMAP_HO_TEN_UNICODE, finish]);

    const onFormValuesChange = useCallback(
      (changedValues, allValues) => {
        if (changedValues.hasOwnProperty("tenNb")) {
          const tenNb = allValues.tenNb;
          const value = chuyenDoiTenNb(tenNb.toUpperCase(), mapHoTenUnicode);
          const newData = { ["tenNb"]: value };
          if (!allValues.gioiTinh) {
            let genderVan = value.search(" VĂN ");
            let genderThi = value.search(" THỊ ");
            if (genderVan >= 0 && genderThi < 0) {
              newData.gioiTinh = GIOI_TINH.NAM;
            } else if (genderThi >= 0) {
              newData.gioiTinh = GIOI_TINH.NU;
            }
          }
          form.setFieldsValue(newData);
        }
      },
      [form, mapHoTenUnicode]
    );

    const handleSubmit = (values) => {
      const { diaChi, ...rest } = values;
      let data = {
        ...rest,
        ngaySinh: rest.ngaySinh?.date || state.ngaySinh?.date,
        quocGiaId: state.quocGiaId,
        tinhThanhPhoId: state.tinhThanhPhoId,
        xaPhuongId: state.xaPhuongId,
      };
      form.resetFields();
      onFinish(data);
    };

    const showUuTien = useMemo(() => {
      return checkRole(["kiosk_dangKy_uuTien"]);
    }, []);

    return (
      <>
        <Form
          name="dangKyKham"
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          style={{ width: "100%" }}
          className="form-custom"
          onValuesChange={onFormValuesChange}
        >
          <Row gutter={[16, 24]}>
            <Col span={24}>
              <Form.Item
                label={t("common.hoVaTen")}
                name="tenNb"
                rules={[
                  {
                    required: true,
                    message: t("kiosk.vuiLongNhapHoVaTen"),
                  },
                ]}
              >
                <Input
                  placeholder={t("common.nhapHoVaTen")}
                  allowClear
                  autoComplete="off"
                />
              </Form.Item>
            </Col>

            <Col span={14}>
              <Form.Item
                label={t("kiosk.ngayThangNamSinh")}
                className="custom-label"
                name="ngaySinh"
                rules={[
                  {
                    required: true,
                    validator: (_, value) => {
                      if (!value?.str) {
                        return Promise.reject(
                          t("kiosk.vuiLongNhapNgayThangNamSinh")
                        );
                      }
                      return Promise.resolve();
                    },
                  },
                  {
                    validator: validateNgaySinh,
                  },
                ]}
              >
                <DOBInput
                  allowClear
                  className="item-dob"
                  value={state.ngaySinh}
                  onBlur={handleDOBBlur}
                  onChange={handleDOBChange}
                  placeholder={t("tiepDon.nhapNgayThangNamSinh")}
                />
              </Form.Item>
              <div className="helper-text">
                {t("kiosk.viDu")}: 12/05/1990 {t("kiosk.hoac")} 12051990
              </div>
            </Col>

            <Col span={10}>
              <Form.Item
                label={t("common.gioiTinh")}
                name="gioiTinh"
                rules={[
                  {
                    required: true,
                    message: t("common.vuiLongChonGioiTinh"),
                  },
                ]}
              >
                <Radio.Group>
                  {listGioiTinhMemo.map((item) => (
                    <Radio key={item.id} value={item.id}>
                      {item.ten}
                    </Radio>
                  ))}
                </Radio.Group>
              </Form.Item>
            </Col>

            <Col span={showUuTien ? 14 : 24}>
              <Form.Item
                label={t("common.soDienThoai")}
                name="soDienThoai"
                rules={[
                  {
                    required: true,
                    message: t("tiepDon.vuiLongNhapSoDienThoai"),
                  },
                  {
                    validator(_, value) {
                      if (value && !value.replaceAll(" ", "").isPhoneNumber()) {
                        return Promise.reject(
                          new Error(t("tiepDon.soDienThoaiSaiDinhDang"))
                        );
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <Input
                  placeholder={t("common.nhapSoDienThoai")}
                  allowClear
                  autoComplete="off"
                />
              </Form.Item>
            </Col>
            {showUuTien && (
              <Col span={10}>
                <Form.Item label={" "} name={"uuTien"} valuePropName="checked">
                  <Checkbox>{t("common.uuTien")}</Checkbox>
                </Form.Item>
              </Col>
            )}
            <Col span={24}>
              <Form.Item
                label={t("common.soGiayToTuyThan")}
                name="maSoGiayToTuyThan"
                rules={[
                  {
                    required: true,
                    message: t("kiosk.vuiLongNhapSoGiayToTuyThan"),
                  },
                ]}
              >
                <Input
                  placeholder={t("kiosk.nhapSoGiayToTuyThan")}
                  allowClear
                  autoComplete="off"
                />
              </Form.Item>
            </Col>

            <Col span={24}>
              <Form.Item label={t("kiosk.soNhaThonXom")} name="soNha">
                <Input
                  placeholder={t("kiosk.nhapSoNha")}
                  allowClear
                  autoComplete="off"
                />
              </Form.Item>
              <div className="helper-text">Ví dụ: 2018 Nguyễn Chí Thanh</div>
            </Col>

            <Col span={24}>
              <Form.Item
                label={t("tiemChung.phuongXaTinhThanh")}
                name="diaChi"
                rules={[
                  {
                    required: true,
                    message: t("kiosk.vuiLongNhapDiaChi"),
                  },
                ]}
              >
                <AddressFull
                  onChangeAdrressText={onChangeAdrressText}
                  onBlur={(e) => {
                    const value = e.target.value;
                    form.setFields([
                      {
                        name: "diaChi",
                        value: value,
                        errors: value && value.trim() ? [] : undefined,
                      },
                    ]);
                  }}
                  ref={refAddressFull}
                  value={state.diaChi}
                  placeholder={t("nhaThuoc.nhapPhuongXaTinhThanhPho")}
                  onSelectAddress={selectAdress}
                  onError={onErrorAddress}
                  delayTyping={300}
                  wrapperClassName="address-full-wrapper"
                />
              </Form.Item>
              <div className="helper-text">
                Ví dụ: Chợ Lớn, Hồ Chí Minh hoặc nhập viết tắt chữ cái đầu CLCM
              </div>
            </Col>
          </Row>
        </Form>
      </>
    );
  })
);

FormDangKyKham.defaultProps = {
  otherInfo: null,
};

export default FormDangKyKham;
