import React, { useEffect, useRef, useCallback, useState } from "react";
import { TableWrapper, Pagination, ChonQuay } from "components";
import { Main } from "./styled";
import { useDispatch, useSelector } from "react-redux";
import { columns } from "./general";
import { useLocation, useParams } from "react-router-dom";
import { useTranslation } from "react-i18next";
import ModalTamUng from "pages/thuNgan/quanLyTamUng/chiTietQuanLyTamUng/Modal/ModalTamUng";
import printProvider from "data-access/print-provider";
import {
  CACHE_KEY,
  TRANG_THAI_THANH_TOAN_QR,
  THIET_LAP_CHUNG,
  ENUM,
  LOAI_MH_PHU,
  LOAI_PHUONG_THUC_TT,
  LOAI_QUAY,
  VI_TRI_PHIEU_IN,
} from "constants/index";
import {
  useQueryString,
  useListAll,
  useThietLap,
  useEnum,
  useStore,
  useConfirm,
  useLoading,
} from "hooks";
import { isNumber, sleep, isArray, isObject } from "utils/index";
import ModalTaoQrCode from "../../Modal/ModalTaoQrCode";
import ModalTaoQrCodeLoi from "../../Modal/ModalTaoQrCodeLoi";
import { message } from "antd";
import { ISOFH_TOOL_HOST } from "client/request";
import ModalThongBaoThanhToanQrCode from "pages/thuNgan/chiTietPhieuThu/ModalThongBaoThanhToanQrCode";
import nbTamUngProvider from "data-access/nb-tam-ung-provider";
import isofhToolProvider from "data-access/isofh-tool-provider";

const DanhSach = (props) => {
  const refChonQuayTiepDon = useRef(null);
  const [state, _setState] = useState({
    nhaTamUng: null,
    listAllQuayTiepDon: [],
  });
  const setState = (data = {}) => {
    _setState((preState) => {
      return { ...preState, ...data };
    });
  };

  const { showConfirm } = useConfirm();
  const { isNoiTru, nbDotDieuTriId: nbId } = props;
  const { t } = useTranslation();
  const { dataSortColumn, totalElements, page, size, listDsDeNghiTamUng } =
    useSelector((state) => state.deNghiTamUng);
  // dùng useSelector để watch chính xác sự thay đổi của listDsDeNghiTamUng
  // dùng useStore sẽ bị 2 lần trong useEffect
  const isTamUng = window.location.pathname.includes(
    "/thu-ngan/quan-ly-tam-ung"
  );

  const thongTinBenhNhan = useStore("nbDotDieuTri.thongTinBenhNhan");
  const thongTinCoBan = useStore("nbDotDieuTri.thongTinCoBan");
  const refModalTaoQrCode = useRef(null);
  const refModalTaoQrCodeLoi = useRef(null);
  const refInterval = useRef(null);
  const refCurrentMsg = useRef(null);
  const refModalThongBaoThanhToanQrCode = useRef(null);
  const { state: locationState } = useLocation();
  const [nhaTamUngId] = useQueryString("nhaTamUngId", null);
  const [listAllPhuongThucThanhToan] = useListAll("phuongThucTT", {}, true);
  const [listAllToaNha] = useListAll("toaNha", {}, true);
  const listAllDoiTacThanhToan = useStore("doiTac.listAllDoiTacThanhToan", []);
  const nhanVienId = useStore("auth.auth.nhanVienId", null);
  const [dataIN_PHIEU_KHI_DE_NGHI_TAM_UNG] = useThietLap(
    THIET_LAP_CHUNG.IN_PHIEU_KHI_DE_NGHI_TAM_UNG
  );
  const [dataMA_DOI_TAC_QRCODE_CAP_NHAT_GIAO_DICH] = useThietLap(
    THIET_LAP_CHUNG.MA_DOI_TAC_QRCODE_CAP_NHAT_GIAO_DICH
  );
  const [listTrangThaiThanhToan] = useEnum(ENUM.TRANG_THAI_THANH_TOAN);
  const { showLoading, hideLoading } = useLoading();

  const {
    quanLyTamUng: { onDelete },
    deNghiTamUng: {
      onSizeChange,
      onSortChange,
      onSearch,
      duyetDeNghiTamUng,
      suaDeNghiTamUng,
    },
    thuNgan: {
      kiemTraTrangThaiThanhToanQr,
      huyQrThanhToan,
      taoQrThanhToan,
      kiemTraGiaoDich,
    },
    thuTamUng: { onChangeInputSearch, inPhieuTamUng },
    doiTac: { getListAllDoiTacThanhToan },
    quayTiepDon: { getListAllQuayTiepDon },
  } = useDispatch();

  const { id } = useParams();
  const refSettings = useRef(null);
  const refModalDeNghiTamUng = useRef(null);
  const nbDotDieuTriId = nbId || id;

  const isDoiTuongKcbNoiTru = [2, 3, 4, 6, 9].includes(
    thongTinBenhNhan?.doiTuongKcb ?? thongTinCoBan?.doiTuongKcb
  );

  const paramsLienin = {
    maViTri: VI_TRI_PHIEU_IN.THU_NGAN.IN_PHIEU_THU_TAM_UNG,
    maPhieuIn: isDoiTuongKcbNoiTru ? "P1100" : "P1099",
  };

  const onChangePage = (page) => {
    onSearch({ page: page - 1 });
  };

  const handleSizeChange = (size) => {
    onSizeChange({ size });
  };

  const onClickSort = (key, value) => {
    onSortChange({
      [key]: value,
    });
  };

  const onRow = (record) => {
    return {
      onClick: () => {
        // history.push("/thu-ngan/quan-ly-tam-ung/" + record.id);
      },
    };
  };

  const onSettings = () => {
    refSettings && refSettings.current.settings();
  };

  useEffect(() => {
    const { duyetDeNghiTamUng, data, nhaTamUng } = locationState || {};
    if (duyetDeNghiTamUng) {
      onShowDuyetTamUng({ ...data, nhaTamUng });
    }
  }, [locationState]);

  useEffect(() => {
    getListAllDoiTacThanhToan({ page: "", size: "", active: true });
    getListAllQuayTiepDon({ page: "", size: "", active: true }).then((res) => {
      setState({ listAllQuayTiepDon: res });
    });
    return () => {
      refInterval.current && clearInterval(refInterval.current);
      clearFunc(true);
    };
  }, []);

  const kiemTraTrangThaiQrThanhToan = async (item) => {
    try {
      let params = {
        nbDotDieuTriId,
        loai: 10,
        tuBanGhiId: item?.id,
      };
      const res = await kiemTraTrangThaiThanhToanQr(params);
      const { data } = res || {};
      const message = data?.phanHoi?.message;
      if (data?.trangThai === TRANG_THAI_THANH_TOAN_QR.THANH_TOAN) {
        // Show popup thành công
        refInterval.current && clearInterval(refInterval.current);

        isofhToolProvider.putDataCenter({
          ma: "TT_TAM_UNG",
          value: { ...item, trangThaiThanhToan: data.trangThai },
        });

        // ẩn popup qr xem qr
        refModalTaoQrCode.current && refModalTaoQrCode.current.hide();

        // show popup thanh toán qr thành công
        refModalThongBaoThanhToanQrCode.current &&
          refModalThongBaoThanhToanQrCode.current.show(
            {
              ...data,
              type: "success",
              title: t("thuNgan.thanhToanThanhCong"),
            },
            () => {
              onInPhieuTamUng(item.id);
              onOkSuaPhieu();
            }
          );
      } else if (data?.trangThai !== TRANG_THAI_THANH_TOAN_QR.TAO_QR) {
        refInterval.current && clearInterval(refInterval.current);
      } else {
        const showWarningPopup = () => {
          refModalThongBaoThanhToanQrCode.current &&
            refModalThongBaoThanhToanQrCode.current.show({
              ...data,
              type: "warning",
              title: t("common.canhBao"),
            });
        };

        if (message === null) {
          refCurrentMsg.current = message;
        } else if (!!message) {
          if (
            refCurrentMsg.current === null &&
            refCurrentMsg.current !== message
          ) {
            // Show popup lỗi
            showWarningPopup();
          } else if (refCurrentMsg.current !== message) {
            // Show popup lỗi
            showWarningPopup();
          }
          refCurrentMsg.current = message;
        }
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleCheckQrTamUng = useCallback(async () => {
    if (
      isArray(listDsDeNghiTamUng, true) &&
      isArray(listAllPhuongThucThanhToan, true)
    ) {
      try {
        let res = await nbTamUngProvider.search({
          nbDotDieuTriId,
          trangThai: 10,
          page: 0,
          size: 500,
          sort: "thoiGianDeNghi,desc",
        });
        res = res?.data;
        let qrData = null;
        if (isArray(res, true)) {
          for (const item of res) {
            const pttt = listAllPhuongThucThanhToan.find(
              (i) => i.id === item.phuongThucTtId
            );
            if (
              pttt?.loaiPhuongThucTt === LOAI_PHUONG_THUC_TT.QR_CODE &&
              item.trangThaiThanhToan === TRANG_THAI_THANH_TOAN_QR.TAO_QR
            ) {
              qrData = item;
              break;
            }
          }
        }
        if (qrData) {
          refInterval.current && clearInterval(refInterval.current);
          await kiemTraTrangThaiQrThanhToan(qrData);
          refInterval.current = setInterval(() => {
            kiemTraTrangThaiQrThanhToan(qrData);
          }, 5000);
        }
      } catch (error) {
        console.error(error);
      }

      return () => {
        refInterval.current && clearInterval(refInterval.current);
      };
    }
  }, [listAllPhuongThucThanhToan, listDsDeNghiTamUng]);

  useEffect(() => {
    handleCheckQrTamUng();
    return () => {
      refInterval.current && clearInterval(refInterval.current);
    };
  }, [handleCheckQrTamUng]);

  const clearFunc = (isLeave = false) => {
    if (isLeave) {
      isofhToolProvider.putDataCenter({ ma: "LOAI_MH_PHU", value: null });
    }
    isofhToolProvider.putDataCenter({ ma: "QR_VALUE", value: null });
    isofhToolProvider.putDataCenter({ ma: "TT_NB", value: {} });
    isofhToolProvider.putDataCenter({ ma: "TT_TAM_UNG", value: {} });
  };

  const showModaConfirmRemove = (data) => {
    showConfirm(
      {
        title: "",
        content: `${t("thuNgan.quanLyTamUng.xacNhanXoaDeNghi")}`,
        cancelText: t("common.huy"),
        okText: t("common.dongY"),
        typeModal: "warning",
        showBtnOk: true,
      },
      () => {
        onDelete(typeof data === "object" ? data?.id : data).then(() => {
          onSearch({
            dataSearch: {
              nbDotDieuTriId,
              trangThai: 10,
            },
          });
        });
      },
      () => {}
    );
  };

  const onOkSuaPhieu = () => {
    onSearch({
      dataSearch: { nbDotDieuTriId, trangThai: 10 },
    });
  };

  const onOkDuyetDeNghi = (idPhieuThu) => {
    onSearch({
      dataSearch: { nbDotDieuTriId, trangThai: 10 },
    });
    onChangeInputSearch({
      nbDotDieuTriId,
      dsTrangThai: [40, 50, 15],
    });
    onInPhieuTamUng(idPhieuThu);
  };

  const onInPhieuTamUng = async (idPhieuThu) => {
    try {
      const s = await inPhieuTamUng(idPhieuThu, paramsLienin);
      await printProvider.printPdf(s);
    } catch (error) {
      console.error(error);
    }
  };

  const onCapNhatTrangThaiGiaoDichQr = async (id) => {
    try {
      showLoading();

      await kiemTraGiaoDich(id);
      await sleep(300);

      onChangeInputSearch({
        nbDotDieuTriId,
        dsTrangThai: [40, 50, 15],
      });
    } catch (error) {
      console.error(error);
    } finally {
      hideLoading();
    }
  };

  const onTaoQrThanhToan = (item) => {
    showLoading({ title: t("thuNgan.dangGenQrCode"), width: 300 });
    taoQrThanhToan({
      nbDotDieuTriId,
      loai: 10,
      tuBanGhiId: item.id,
      nguoiTaoId: nhanVienId,
    })
      .then(async (res) => {
        const { trangThai, qr, phanHoi } = res?.data || {};
        if (phanHoi && phanHoi.code !== "00") {
          refModalTaoQrCodeLoi.current &&
            refModalTaoQrCodeLoi.current.show(
              { message: phanHoi?.message },
              () => {
                onTaoQrThanhToan(item, true);
              },
              () => {
                showModaConfirmRemove(item.id);
              }
            );
        } else if (isNumber(dataIN_PHIEU_KHI_DE_NGHI_TAM_UNG)) {
          switch (Number(dataIN_PHIEU_KHI_DE_NGHI_TAM_UNG)) {
            case 1:
              onInPhieuTamUng(item.id);
              break;
            case 2:
            case 3:
              if (
                [
                  TRANG_THAI_THANH_TOAN_QR.MOI,
                  TRANG_THAI_THANH_TOAN_QR.TAO_QR,
                  TRANG_THAI_THANH_TOAN_QR.THANH_TOAN,
                ].includes(trangThai)
              ) {
                // in phiếu tạm ứng vs case = 3
                if (Number(dataIN_PHIEU_KHI_DE_NGHI_TAM_UNG) === 3) {
                  await onInPhieuTamUng(item.id);
                  await sleep(1500);
                }

                //đẩy dữ liệu sang mh phụ
                isofhToolProvider.putDataCenter({
                  ma: "LOAI_MH_PHU",
                  value: LOAI_MH_PHU.TAM_UNG,
                });
                isofhToolProvider.putDataCenter({
                  ma: "QR_VALUE",
                  value: res?.data,
                });
                isofhToolProvider.putDataCenter({
                  ma: "TT_NB",
                  value: thongTinBenhNhan,
                });
                isofhToolProvider.putDataCenter({
                  ma: "TT_TAM_UNG",
                  value: item,
                });

                //kiểm tra trạng thái đề nghị tạm ứng
                if (trangThai === TRANG_THAI_THANH_TOAN_QR.TAO_QR) {
                  refInterval.current && clearInterval(refInterval.current);
                  await kiemTraTrangThaiQrThanhToan(item);
                  refInterval.current = setInterval(() => {
                    kiemTraTrangThaiQrThanhToan(item);
                  }, 5000);
                }
                refModalTaoQrCode.current &&
                  refModalTaoQrCode.current.show({ qrData: qr }, () => {
                    clearFunc(false);
                    if (refInterval.current) {
                      clearInterval(refInterval.current);
                    }
                  });
              } else if (TRANG_THAI_THANH_TOAN_QR.TAO_QR_LOI === trangThai) {
                refModalTaoQrCodeLoi.current &&
                  refModalTaoQrCodeLoi.current.show(
                    {},
                    () => {
                      onTaoQrThanhToan(item, true);
                    },
                    () => {
                      showModaConfirmRemove(item.id);
                    }
                  );
              }
              break;
            case 0:
            default:
              break;
          }
        } else {
          onInPhieuTamUng(item.id);
        }
      })
      .catch((err) => {
        refModalTaoQrCodeLoi.current &&
          refModalTaoQrCodeLoi.current.show(
            { message: err?.message },
            () => {
              onTaoQrThanhToan(item, true);
            },
            () => {
              showModaConfirmRemove(item.id);
            }
          );
      })
      .finally(() => {
        onOkSuaPhieu();
        hideLoading();
      });
  };

  const onShowDuyetTamUng = (item) => {
    const { nhaTamUng: _nhaTamUng, ...rest } = item || {};
    if (!state.nhaTamUng && !isNumber(nhaTamUngId) && !_nhaTamUng) {
      refChonQuayTiepDon.current &&
        refChonQuayTiepDon.current.onSelectQuay((data) => {
          setState({ nhaTamUng: data });
        });
    } else {
      refModalDeNghiTamUng.current &&
        refModalDeNghiTamUng.current.show(
          {
            ...rest,
            tab: "thuTamUng",
            action: duyetDeNghiTamUng,
            parentNhaTamUng: state.nhaTamUng,
            isDuyet: true,
            focusInput: true,
          },
          ({ id: idPhieuThu, res: s }) => {
            if (s?.code === 1036) {
              let msg = s?.message || "";
              msg = msg.replace(
                /\((.*?)\)/,
                "<b style='color: red; font-size: 16px;text-transform: uppercase;'>($1)</b>"
              );
              showConfirm({
                title: t("common.thongBao"),
                content: msg,
                cancelText: t("common.huy"),
                classNameOkText: "button-warning",
                showBtnOk: false,
                typeModal: "warning",
              });
            } else {
              onOkDuyetDeNghi(idPhieuThu);
            }
          }
        );
    }
  };

  const onShowEditDeNghiTamUng = (item) => {
    refModalDeNghiTamUng.current &&
      refModalDeNghiTamUng.current.show(
        { ...item, tab: "deNghiTamUng", action: suaDeNghiTamUng },
        ({ isQrCode, res: data, submitFunc, body, handleClickBack }) => {
          if (data?.code === 1025) {
            if (
              [
                TRANG_THAI_THANH_TOAN_QR.MOI,
                TRANG_THAI_THANH_TOAN_QR.TAO_QR,
                TRANG_THAI_THANH_TOAN_QR.TAO_QR_LOI,
              ].includes(item.trangThaiThanhToan)
            ) {
              refModalTaoQrCodeLoi.current &&
                refModalTaoQrCodeLoi.current.show(
                  { message: data?.message, isHuyQrCode: true },
                  () => {
                    onHuyQrCode(item?.thanhToanId, {
                      submitFunc,
                      body,
                      handleClickBack,
                    });
                  },
                  () => {}
                );
            } else {
              message.error(data?.message);
              return;
            }
          } else if (isQrCode) {
            setTimeout(() => {
              onTaoQrThanhToan(item);
            }, 301);
          }
          onOkSuaPhieu();
        }
      );
  };

  const onViewQrCode = (item) => {
    const qrData = item?.qrThanhToan;

    //đẩy dữ liệu sang mh phụ
    isofhToolProvider.putDataCenter({
      ma: "LOAI_MH_PHU",
      value: LOAI_MH_PHU.TAM_UNG,
    });
    isofhToolProvider.putDataCenter({ ma: "QR_VALUE", value: item });
    isofhToolProvider.putDataCenter({ ma: "TT_NB", value: thongTinBenhNhan });
    isofhToolProvider.putDataCenter({ ma: "TT_TAM_UNG", value: item });

    //kiểm tra trạng thái đề nghị tạm ứng
    if (item.trangThaiThanhToan === TRANG_THAI_THANH_TOAN_QR.TAO_QR) {
      refInterval.current && clearInterval(refInterval.current);
      refInterval.current = setInterval(() => {
        kiemTraTrangThaiQrThanhToan(item);
      }, 5000);
    }

    refModalTaoQrCode.current &&
      refModalTaoQrCode.current.show({ qrData }, () => {
        clearFunc(false);
        if (refInterval.current) {
          clearInterval(refInterval.current);
        }
      });
  };

  const onHuyQrCode = (item, { submitFunc, body, handleClickBack } = {}) => {
    showConfirm(
      {
        title: "",
        content: `${t("thuNgan.quanLyTamUng.xacNhanHuyQrCode")}`,
        cancelText: t("common.quayLai"),
        okText: t("common.xacNhan"),
        typeModal: "warning",
        showBtnOk: true,
      },
      () => {
        showLoading();
        huyQrThanhToan({ thanhToanId: item })
          .then(() => {
            isofhToolProvider.putDataCenter({ ma: "QR_VALUE", value: null });
            if (isObject(body, true) && submitFunc) {
              submitFunc(body).then((s) => {
                handleClickBack();
                onSearch({
                  dataSearch: {
                    nbDotDieuTriId,
                    trangThai: 10,
                  },
                });
              });
            } else {
              onSearch({
                dataSearch: {
                  nbDotDieuTriId,
                  trangThai: 10,
                },
              });
            }
          })
          .finally(() => {
            hideLoading();
          });
      },
      () => {}
    );
  };

  return (
    <Main>
      <TableWrapper
        columns={columns({
          onClickSort,
          onSettings,
          dataSortColumn,
          onShowDuyetTamUng,
          showModaConfirmRemove,
          t,
          onShowEditDeNghiTamUng,
          onInPhieuTamUng,
          onHuyQrCode,
          onViewQrCode,
          isNoiTru,
          listAllPhuongThucThanhToan,
          listAllDoiTac: listAllDoiTacThanhToan,
          listTrangThaiThanhToan,
          maDoiTacQrCodeCapNhatGiaoDich:
            dataMA_DOI_TAC_QRCODE_CAP_NHAT_GIAO_DICH?.eval(),
          onCapNhatTrangThaiGiaoDichQr,
          listAllQuayTiepDon: state.listAllQuayTiepDon,
          listAllToaNha,
        })}
        dataSource={listDsDeNghiTamUng}
        onRow={onRow}
        rowKey={(record) => `${record.id}`}
        scroll={{ x: isNoiTru ? 1000 : 1500 }}
        tableName="tableDeNghiTamUng"
        ref={refSettings}
        classNameRow={"custom-header"}
        styleContainerButtonHeader={{
          display: "flex",
          width: "100%",
          justifyContent: "flex-end",
          alignItems: "center",
          paddingRight: 35,
        }}
      />
      {!!totalElements && (
        <Pagination
          onChange={onChangePage}
          current={page + 1}
          pageSize={size}
          listData={listDsDeNghiTamUng}
          total={totalElements}
          onShowSizeChange={handleSizeChange}
          stylePagination={{ justifyContent: "flex-start" }}
        />
      )}
      <ModalTamUng ref={refModalDeNghiTamUng} />
      <ModalTaoQrCode nbDotDieuTriId={nbDotDieuTriId} ref={refModalTaoQrCode} />
      <ModalTaoQrCodeLoi ref={refModalTaoQrCodeLoi} />
      <ModalThongBaoThanhToanQrCode
        nbDotDieuTriId={nbDotDieuTriId}
        ref={refModalThongBaoThanhToanQrCode}
      />
      {isTamUng && (
        <ChonQuay
          ref={refChonQuayTiepDon}
          dsLoaiQuay={LOAI_QUAY.THU_NGAN}
          cacheKey={CACHE_KEY.DATA_NHA_TAM_UNG}
          onChange={(value) => {
            setState({ nhaTamUng: value });
          }}
          showContent={false}
        />
      )}
    </Main>
  );
};

export default DanhSach;
