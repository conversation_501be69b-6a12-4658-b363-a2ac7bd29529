import { ROLES } from "constants/index";
import { Page } from "pages/constants";
import React from "react";
const ThietLapQMSDoc = React.lazy(() => import("pages/qms/qmsDoc/ThietLapQMS"));
const ThietLapQMSNgang = React.lazy(() =>
  import("pages/qms/qmsNgang/ThietLapQMS")
);
const QmsDocKhamBenh = React.lazy(() => import("pages/qms/qmsDoc/khamBenh"));
const QmsNgangKhamBenh = React.lazy(() =>
  import("pages/qms/qmsNgang/khamBenh")
);
const QmsDocChanDoanHinhAnh = React.lazy(() =>
  import("pages/qms/qmsDoc/chanDoanHinhAnh")
);
const QmsNgangChanDoanHinhAnh = React.lazy(() =>
  import("pages/qms/qmsNgang/chanDoanHinhAnh")
);
const QmsNgangChanDoanHinhAnh2 = React.lazy(() =>
  import("pages/qms/qmsNgang/chanDoanHinhAnh2")
);
const QmsDocXetNghiem = React.lazy(() => import("pages/qms/qmsDoc/xetNghiem"));
const QmsNgangXetNghiem = React.lazy(() =>
  import("pages/qms/qmsNgang/xetNghiem")
);
// const QmsTiepDon = React.lazy(() => import("pages/qms/qmsNgang/tiepDon2"));
const QmsNgangTiepDon = React.lazy(() =>
  import("pages/qms/qmsNgang/tiepDonChung")
);
const QmsThuNgan = React.lazy(() =>
  import("pages/qms/qmsNgang/QmsThuNgan")
);
const QmsThuNgan2 = React.lazy(() =>
  import("pages/qms/qmsNgang/QmsThuNgan2")
);
const QmsDocTiepDon = React.lazy(() => import("pages/qms/qmsDoc/tiepDonChung"));

//qms pttt
const QmsNgangPhauThuatThuThuat = React.lazy(() =>
  import("pages/qms/qmsNgang/phauThuatThuThuat")
);
const QmsNgangPhauThuatThuThuat2 = React.lazy(() =>
  import("pages/qms/qmsNgang/phauThuatThuThuat2")
);
const QmsNgangKhamBenh3 = React.lazy(() =>
  import("pages/qms/qmsNgang/khamBenh3")
);
const QmsNgangKhamBenh4 = React.lazy(() =>
  import("pages/qms/qmsNgang/khamBenh4")
);
const QmsNgangKhamBenh5 = React.lazy(() =>
  import("pages/qms/qmsNgang/khamBenh4/index2")
);
const QmsNgangChanDoanHinhAnh4 = React.lazy(() =>
  import("pages/qms/qmsNgang/chanDoanHinhAnh4")
);
const QmsNgangNoiTru = React.lazy(() => import("pages/qms/qmsNgang/noiTru"));
const QmsNgangTuSoDenSo = React.lazy(() =>
  import("pages/qms/qmsNgang/tuSoDenSo")
);
const QmsNgangPhatThuocBhyt = React.lazy(() =>
  import("pages/qms/qmsNgang/phatThuocBhyt")
);

export default {
  qmsDoc: {
    component: Page(ThietLapQMSDoc, []),
    accessRoles: [],
    path: "/qms/thiet-lap-doc",
    exact: true,
  },
  qmsNgang: {
    component: Page(ThietLapQMSNgang, []),
    accessRoles: [ROLES["HE_THONG"].QMS],
    path: "/qms/thiet-lap-ngang",
    exact: true,
  },
  qmsDocKhamBenh: {
    component: Page(QmsDocKhamBenh, []),
    accessRoles: [],
    path: ["/qms/qms-doc/kham-benh"],
    exact: true,
  },
  qmsNgangKhamBenh: {
    component: Page(QmsNgangKhamBenh, []),
    accessRoles: [],
    path: ["/qms/qms-ngang/kham-benh"],
    exact: true,
  },
  qmsDocChanDoanHinhAnh: {
    component: Page(QmsDocChanDoanHinhAnh, []),
    accessRoles: [],
    path: ["/qms/qms-doc/chan-doan-hinh-anh"],
    exact: true,
  },
  qmsNgangChanDoanHinhAnh: {
    component: Page(QmsNgangChanDoanHinhAnh, []),
    accessRoles: [],
    path: ["/qms/qms-ngang/chan-doan-hinh-anh"],
    exact: true,
  },
  qmsNgangChanDoanHinhAnh2: {
    component: Page(QmsNgangChanDoanHinhAnh2, []),
    accessRoles: [],
    path: ["/qms/qms-ngang/chan-doan-hinh-anh2"],
    exact: true,
  },
  qmsNgangXetNghiem: {
    component: Page(QmsNgangXetNghiem, []),
    accessRoles: [],
    path: ["/qms/qms-ngang/xet-nghiem"],
    exact: true,
  },
  qmsDocXetNghiem: {
    component: Page(QmsDocXetNghiem, []),
    accessRoles: [],
    path: ["/qms/qms-doc/xet-nghiem"],
    exact: true,
  },
  // qmsDocKhamBenh2: {
  //   component: Page(QmsDocKhamBenh2, []),
  //   accessRoles: [],
  //   path: ["/qms/qms-doc/kham-benh2"],
  //   exact: true,
  // },
  qmsTiepDon: {
    component: Page(QmsNgangTiepDon, []),
    accessRoles: [],
    path: [
      "/qms/qms-ngang/tiep-don",
      "/qms/qms-ngang/thu-ngan",
      "/qms/qms-ngang/kham-benh2",
      "/qms/qms-ngang/kham-benh2_1",
      "/qms/qms-ngang/chan-doan-hinh-anh3",
      "/qms/qms-ngang/chan-doan-hinh-anh3_1",
    ],
    exact: true,
  },
  qmsNgangKhamBenh3: {
    component: Page(QmsNgangKhamBenh3, []),
    accessRoles: [],
    path: ["/qms/qms-ngang/kham-benh3"],
    exact: true,
  },
  //thay thế qms của tekmedi chợ rẫy
  qmsNgangKhamBenh4: {
    component: Page(QmsNgangKhamBenh4, []),
    accessRoles: [],
    path: ["/qms/qms-ngang/kham-benh4", "/qms/qms-ngang/chan-doan-hinh-anh5", "/qms/qms-ngang/xet-nghiem2"],
    exact: true,
  },
  //thay thế qms của tekmedi chợ rẫy
  qmsNgangKhamBenh5: {
    component: Page(QmsNgangKhamBenh5, []),
    accessRoles: [],
    path: ["/qms/qms-ngang/kham-benh5"],
    exact: true,
  },
  qmsThuNgan2: {
    component: Page(QmsThuNgan, []),
    accessRoles: [],
    path: ["/qms/qms-ngang/thu-ngan2"],
    exact: true,
  },
  //Màn hình qms thu ngân tổng số bệnh nhân theo khu vực
  qmsThuNgan3: {
    component: Page(QmsThuNgan2, []),
    accessRoles: [],
    path: ["/qms/qms-ngang/thu-ngan3"],
    exact: true,
  },
  qmsDocTiepDon: {
    component: Page(QmsDocTiepDon, []),
    accessRoles: [],
    path: [
      "/qms/qms-doc/tiep-don",
      "/qms/qms-doc/thu-ngan",
      "/qms/qms-doc/kham-benh2",
      "/qms/qms-doc/chan-doan-hinh-anh3",
    ],
    exact: true,
  },
  // qmsNgangThuNgan: {
  //   component: Page(QmsNgangThuNgan, []),
  //   accessRoles: [],
  //   path: ["/qms/qms-ngang/thu-ngan"],
  //   exact: true,
  // },
  // qmsDocThuNgan: {
  //   component: Page(QmsDocThuNgan, []),
  //   accessRoles: [],
  //   path: ["/qms/qms-doc/thu-ngan"],
  //   exact: true,
  // },
  qmsNgangPhauThuatThuThuat: {
    component: Page(QmsNgangPhauThuatThuThuat, []),
    accessRoles: [],
    path: ["/qms/qms-ngang/phong-pttt", "/qms/qms-ngang/phong-pttt1_1"],
    exact: true,
  },
  qmsNgangPhauThuatThuThuat2: {
    component: Page(QmsNgangPhauThuatThuThuat2, []),
    accessRoles: [],
    path: ["/qms/qms-ngang/phong-pttt2"],
    exact: true,
  },
  qmsNgangChanDoanHinhAnh4: {
    component: Page(QmsNgangChanDoanHinhAnh4, []),
    accessRoles: [],
    path: ["/qms/qms-ngang/chan-doan-hinh-anh4"],
    exact: true,
  },
  qmsNgangNoiTru: {
    component: Page(QmsNgangNoiTru, []),
    accessRoles: [],
    path: ["/qms/qms-ngang/noi-tru"],
    exact: true,
  },
  qmsNgangTuSoDenSo: {
    component: Page(QmsNgangTuSoDenSo, []),
    accessRoles: [],
    path: ["/qms/qms-ngang/tu-so-den-so"],
    exact: true,
  },
  qmsNgangPhatThuocBhyt: {
    component: Page(QmsNgangPhatThuocBhyt, []),
    accessRoles: [],
    path: ["/qms/qms-ngang/phat-thuoc-bhyt"],
    exact: true,
  },
};
