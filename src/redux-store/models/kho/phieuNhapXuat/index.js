import phieuNhapXuatProvider from "data-access/kho/phieu-nhap-xuat-provider";
import { message } from "antd";
import { cloneDeep, get, reject } from "lodash";
import printProvider from "data-access/print-provider";
import nbDvThuocProvider from "data-access/nb-dv-thuoc-provider";
import stringUtils from "mainam-react-native-string-utils";
import { containText, openInNewTab, roundToDigits } from "utils/index";
import fileUtils from "utils/file-utils";
import { t } from "i18next";
import { getThangSoBanLe, tinhThanhTienVat } from "utils/kho-utils";
import { LOAI_CHIET_KHAU } from "constants";

const initialState = {
  thongTinPhieu: {},
  nhapKhongTheoThau: false,
  dsPhieuNhapXuat: [],
  dsNhapXuatChiTiet: [],
};
export default {
  state: initialState,
  reducers: {
    updateData(state, payload = {}) {
      return { ...state, ...payload };
    },
    clearData: (state) => {
      return initialState;
    },
  },
  effects: (dispatch) => ({
    getById: (id, state) => {
      return new Promise((resolve, reject) => {
        const isNhapNcc = window.location.pathname.includes(
          "/kho/phieu-nhap-nha-cung-cap/chinh-sua/"
        );
        phieuNhapXuatProvider
          .getById(id)
          .then((res) => {
            if (res.data) {
              const thongTinPhieu = res.data || {};
              const dsNhapXuatChiTiet = (
                thongTinPhieu.dsNhapXuatChiTiet || []
              ).map((item, index) => {
                item.index = index + 1;
                item.detachId = stringUtils.guid();
                let thanhTienTruocVat = roundToDigits(
                  item?.loNhap?.giaNhapTruocVat * (item.soLuongSoCap || 0),
                  3
                );
                const { id, ...dichVu } = item.dichVu || {};
                return {
                  ...item,
                  ...(isNhapNcc && {
                    thanhTienTruocVat,
                    thanhTienSuaDoiTruocVat: item.thanhTienSuaDoiTruocVat,
                    thanhTienVat: tinhThanhTienVat(
                      item.thanhTienSuaDoi,
                      item.thanhTienSuaDoiTruocVat
                    ),
                  }),
                  ...dichVu,
                };
              });

              //Do lúc lưu field tienChietKhau = chiết khấu theo % + chiết khấu theo tiền
              //=> khi get lại tính lại chiết khấu riêng theo tiền để tiện tính toán
              const tongTien =
                dsNhapXuatChiTiet?.reduce(
                  (total, x) =>
                    (total =
                      total +
                      (x?.loNhap?.giaNhapSauVat || 0) * (x?.soLuongSoCap || 0)),
                  0
                ) || 0;
              const _chietKhauTheoTien =
                (thongTinPhieu.tienChietKhau || 0) -
                (tongTien * (thongTinPhieu.phanTramChietKhau || 0)) / 100;
              dispatch.phieuNhapXuat.updateData({
                thongTinPhieu: {
                  ...thongTinPhieu,
                  chietKhauTheoTien: _chietKhauTheoTien,
                },
                dsNhapXuatChiTiet,
                dsNhapXuatChiTietFull: cloneDeep(dsNhapXuatChiTiet),
              });
              resolve(res.data);
            } else {
              reject(res);
            }
          })
          .catch((e) => {
            reject(e);
          });
      });
    },
    guiDuyetPhieu: (payload = {}, state) => {
      return new Promise((resolve, reject) => {
        const { id, onSucess, final = () => {} } = payload;
        phieuNhapXuatProvider
          .guiDuyet(id)
          .then(async (res) => {
            if (res && res.code === 0) {
              message.success(payload.message || t("kho.guiDuyetThanhCong"));
              if (res.data) {
                dispatch.phieuNhapXuat.updateData({
                  thongTinPhieu: res.data,
                });
              } else {
                await dispatch.phieuNhapXuat.getById(id);
              }
              if (onSucess) onSucess();
              resolve(res);
            }
          })
          .catch((res) => {
            message.error(res?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(res);
          })
          .finally(final);
      });
    },
    duyetPhieu: (payload = {}, state) => {
      return new Promise((resolve, reject) => {
        const { id, capNhatCoSo } = payload;
        phieuNhapXuatProvider
          .duyetPhieu({ id, capNhatCoSo })
          .then(async (res) => {
            if (res && res.code === 0) {
              message.success(t("kho.duyetThanhCong"));
              if (res.data) {
                dispatch.phieuNhapXuat.updateData({
                  thongTinPhieu: res.data,
                });
              } else {
                await dispatch.phieuNhapXuat.getById(id);
              }
              resolve(res);
            } else {
              message.error(
                res?.message || t("common.xayRaLoiVuiLongThuLaiSau")
              );
              reject(res);
            }
          })
          .catch((res) => {
            message.error(res?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(res);
          });
      });
    },
    huyDuyet: (payload = {}, state) => {
      return new Promise((resolve, reject) => {
        const { id, lyDo } = payload;
        phieuNhapXuatProvider
          .huyDuyet({ id, lyDo })
          .then((res) => {
            if (res && res.code === 0) {
              message.success(t("kho.huyDuyetThanhCong"));
              dispatch.phieuNhapXuat.updateData({
                thongTinPhieu: res.data,
              });
            }
          })
          .catch((res) => {
            message.error(res?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    tuChoiDuyet: (payload = {}, state) => {
      return new Promise((resolve, reject) => {
        const { id, lyDo } = payload;
        phieuNhapXuatProvider
          .tuChoiDuyet({ id, lyDo })
          .then((res) => {
            if (res && res.code === 0) {
              message.success(t("kho.huyGuiDuyetThanhCong"));
              dispatch.phieuNhapXuat.updateData({
                thongTinPhieu: res.data,
              });
              resolve(res);
            }
          })
          .catch((res) => {
            message.error(res?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    xoaPhieu: (payload = {}) => {
      return new Promise((resolve, reject) => {
        const { id } = payload;
        phieuNhapXuatProvider
          .delete(id)
          .then((s) => {
            message.success(t("kho.xoaPhieuThanhCong"));
            resolve(s);
          })
          .catch((res) => {
            message.error(res?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    getDataPhieuNhapDuTru: ({ id, ...payload }, state) => {
      return new Promise(async (resolve, reject) => {
        const thongTinPhieu = state.phieuNhapXuat.thongTinPhieu || {};
        const {
          khoDoiUngId,
          thangDuTru,
          khoId,
          ghiChu,
          hinhThucNhapXuatId,
          dieuChinhCoSoDuoi,
        } = thongTinPhieu;
        const { dsNhapXuatChiTiet = [] } = state.phieuNhapXuat;
        let check = await dispatch.phieuNhapDuTru.isValidData({
          khoDoiUngId,
          thangDuTru,
          khoId,
          khoDoiUngId,
        });
        if (check) {
          check = await dispatch.phieuNhapDuTru.checkSoLuongDuTru();
          if (check) {
            dispatch.phieuNhapXuat.updateData({ checkValidate: false });
            let payload = {
              loaiNhapXuat: 20,
              khoDoiUngId,
              thangDuTru,
              khoId,
              ghiChu,
              dsNhapXuatChiTiet,
              hinhThucNhapXuatId,
              dieuChinhCoSoDuoi,
            };
            if (!id) {
              payload.dsNhapXuatChiTiet = dsNhapXuatChiTiet.map((item) => ({
                dichVuId: item?.dichVuId,
                soLuongSoCap: item?.soLuongSoCapYeuCau,
                soLuongSoCapYeuCau: item?.soLuongSoCapYeuCau,
                ghiChu: item?.ghiChu,
                loNhapId: item?.loNhapId,
                ...(dieuChinhCoSoDuoi && {
                  soLuongCoSoDuoiSoCap: item?.soLuongCoSoDuoiSoCap,
                }),
              }));
            } else {
              payload = { ...thongTinPhieu, ...payload };
              payload.dsNhapXuatChiTiet = dsNhapXuatChiTiet.map((item) => {
                let { soLuong, soLuongCoSoDuoiSoCap, ...rest } = item;
                return {
                  ...rest,
                  ...(dieuChinhCoSoDuoi && {
                    soLuongCoSoDuoiSoCap,
                  }),
                  soLuongSoCap: item?.soLuongSoCapYeuCau,
                };
              });
            }
            resolve(payload);
            return;
          }
        }
        dispatch.phieuNhapXuat.updateData({ checkValidate: true });
        resolve(null);
      });
    },
    getDataPhieuXuat: ({ id, ...payload }, state) => {
      return new Promise(async (resolve, reject) => {
        const thongTinPhieu = state.phieuNhapXuat.thongTinPhieu || {};
        const {
          loaiNhapXuat,
          khoDoiUngId,
          khoId,
          hinhThucNhapXuatId,
          ghiChu,
          nhaCungCapId,
          phieuNhapId,
          phieuDoiUngId,
          dieuChinhCoSoDuoi,
          nguonSuDungKhoId,
        } = thongTinPhieu;
        if (loaiNhapXuat == 30) {
          if (!hinhThucNhapXuatId || !khoId || !khoDoiUngId) {
            dispatch.phieuNhapXuat.updateData({ checkValidate: true });
            resolve(null);
            return;
          }
        } else {
          if (loaiNhapXuat == 40) {
            if (!hinhThucNhapXuatId || !khoId || !nhaCungCapId) {
              dispatch.phieuNhapXuat.updateData({ checkValidate: true });
              resolve(null);
              return;
            }
          } else {
            if (loaiNhapXuat == 90) {
              if (!hinhThucNhapXuatId || !khoId) {
                dispatch.phieuNhapXuat.updateData({ checkValidate: true });
                resolve(null);
                return;
              }
            }
          }
        }

        const { dsNhapXuatChiTiet = [] } = state.phieuNhapXuat;
        let payload = {
          loaiNhapXuat,
          khoDoiUngId,
          hinhThucNhapXuatId,
          khoId,
          ghiChu,
          dsNhapXuatChiTiet,
          nhaCungCapId,
          phieuNhapId,
          phieuDoiUngId,
          dieuChinhCoSoDuoi,
          nguonSuDungKhoId,
        };
        if (!id) {
          payload.dsNhapXuatChiTiet = dsNhapXuatChiTiet.map((item) => ({
            dichVuId: item?.dichVuId,
            soLuongSoCap: item.moi ? item?.soLuongSoCap : null,
            soLuongSoCapYeuCau: item.moi ? item?.soLuongSoCapYeuCau : null,
            soLuong: !item.moi ? item?.soLuong : null,
            soLuongYeuCau: !item.moi ? item?.soLuongYeuCau : null,
            ghiChu: item?.ghiChu,
            loNhapId: item?.loNhapId,
            moi: true,
            ...(dieuChinhCoSoDuoi && {
              soLuongCoSoDuoiSoCap: item?.soLuongCoSoDuoiSoCap,
            }),
          }));
        } else {
          payload = { ...thongTinPhieu, ...payload };
          payload.dsNhapXuatChiTiet = dsNhapXuatChiTiet.map(
            ({ soLuongCoSoDuoiSoCap, ...item }) => {
              return {
                ...item,
                ...(dieuChinhCoSoDuoi && {
                  soLuongCoSoDuoiSoCap,
                }),
                soLuongSoCap: item.moi ? item?.soLuongSoCap || 0 : null,
                soLuong: !item.moi ? item?.soLuongSoCap || 0 : null,
              };
            }
          );
        }
        resolve(payload);
      });
    },
    createOrUpdate: (
      { id, guiDuyet, loaiPhieu, duyetPhieu, ...payload },
      state
    ) => {
      const timeout = (ms) => {
        return new Promise((resolve) => setTimeout(resolve, ms));
      };
      return new Promise(async (resolve, reject) => {
        try {
          let data = { nhapKho: true };
          if (loaiPhieu == 1) {
            //dự trù
            data = await dispatch.phieuNhapXuat.getDataPhieuNhapDuTru({ id });
            if (!data) {
              reject();
              return;
            }
          } else if (loaiPhieu == 3) {
            data = await dispatch.phieuNhapXuat.getDataPhieuXuat({
              id,
            });
            if (!data) {
              reject();
              return;
            }
            data.nhapKho = false;
          }
          if (!id) {
            phieuNhapXuatProvider
              .post({ ...data, ...payload })
              .then(async (s) => {
                if (guiDuyet) {
                  await dispatch.phieuNhapXuat.guiDuyetPhieu({
                    id: s?.data?.id,
                    message: t("kho.themMoiVaGuiDuyetThanhCong"),
                  });
                  if (duyetPhieu) {
                    await timeout(500);
                    dispatch.phieuNhapXuat.duyetPhieu({
                      id: s?.data?.id,
                    });
                  }
                  resolve(s?.data);
                } else {
                  message.success(t("kho.themVaoKhoThanhCong"));
                  dispatch.phieuNhapXuat.updateData({
                    thongTinPhieu: s.data,
                  });
                  resolve(s?.data);
                }
              })
              .catch((e) => {
                message.error(
                  e?.message || t("common.xayRaLoiVuiLongThuLaiSau")
                );
                reject(e);
              });
          } else {
            phieuNhapXuatProvider
              .put({
                id,
                ...data,
                ...payload,
              })
              .then(async (s) => {
                const isNhapNCC = window.location.pathname.includes(
                  "phieu-nhap-nha-cung-cap/chinh-sua"
                );

                if (isNhapNCC && guiDuyet) {
                  await dispatch.phieuNhapXuat.guiDuyetPhieu({
                    id,
                    message: t("kho.chinhSuaVaGuiDuyetThanhCong"),
                  });
                } else {
                  dispatch.phieuNhapXuat.updateData({
                    thongTinPhieu: s.data,
                  });
                  message.success(t("common.capNhatThanhCong"));
                }
                resolve(s.data);
              })
              .catch((res) => {
                message.error(
                  res?.message || t("common.xayRaLoiVuiLongThuLaiSau")
                );
                reject(res);
              });
          }
        } catch (res) {
          message.error(res?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          reject(res);
        }
      });
    },
    setEdit: ({ isEdit = false, clone = true }, state) => {
      const thongTinPhieu = state.phieuNhapXuat.thongTinPhieu || {};
      let thongTinPhieuClone = "";
      if (isEdit) {
        if (clone) thongTinPhieuClone = cloneDeep(thongTinPhieu);
        dispatch.phieuNhapXuat.updateData({
          thongTinPhieu: { ...thongTinPhieu, editMode: isEdit },
          thongTinPhieuClone,
        });
      } else {
        const thongTinPhieuClone = state.phieuNhapXuat.thongTinPhieuClone || {};
        dispatch.phieuNhapXuat.updateData({
          thongTinPhieu: { ...thongTinPhieuClone },
        });
      }
    },
    resetData: (state, payload = {}) => {
      dispatch.phieuNhapXuat.clearData();
    },
    inPhieuLinh: (payload = {}, state) => {
      return new Promise((resolve, reject) => {
        const { id, phieuTongHop, tachLo } = payload;
        phieuNhapXuatProvider
          .inPhieuLinh({ id, phieuTongHop, tachLo })
          .then(async (s) => {
            if (s.data) {
              if (payload.openInNewTab) {
                let url = "";
                if (payload.printMerge) {
                  url = await printProvider.getMergePdf([s.data?.file?.pdf]);
                } else {
                  let urlFileLocal = await fileUtils.getFromUrl({
                    url: fileUtils.absoluteFileUrl(s.data.file.pdf),
                  });
                  const blob = new Blob([new Uint8Array(urlFileLocal)], {
                    type: "application/pdf",
                  });
                  url = window.URL.createObjectURL(blob);
                }
                openInNewTab(url);
                resolve(url);
              } else {
                if (payload.printMerge) {
                  printProvider.printMergePdf([s.data?.file?.pdf]);
                } else {
                  printProvider.printPdf(s.data);
                }
                resolve();
              }
            } else {
              reject();
            }
          })
          .catch((e) => {
            message.error(e?.message);
            reject();
          });
      });
    },
    inPhieuNhapXuat: (payload = {}, state) => {
      return new Promise((resolve, reject) => {
        phieuNhapXuatProvider
          .inPhieuNhapXuat(payload)
          .then((s) => {
            if (s?.data) {
              printProvider.printPdf(s.data);
            }
            resolve(s);
          })
          .catch((e) => {
            message.error(e?.message);
            reject(e);
          });
      });
    },
    inPhieuTra: (payload, state) => {
      return new Promise((resolve, reject) => {
        phieuNhapXuatProvider
          .inPhieuLinh({ id: payload.id })
          .then(async (s) => {
            if (s.data) {
              if (payload.openInNewTab) {
                let urlFileLocal = await fileUtils.getFromUrl({
                  url: fileUtils.absoluteFileUrl(s.data.file.pdf),
                });
                const blob = new Blob([new Uint8Array(urlFileLocal)], {
                  type: "application/pdf",
                });
                urlFileLocal = window.URL.createObjectURL(blob);
                openInNewTab(urlFileLocal);
              } else {
                printProvider.printPdf(s.data);
              }

              resolve();
            }
          })
          .catch(() => {
            reject();
          });
      });
    },
    onSelectItemDuTru: ({ item }, state) => {
      const dsNhapXuatChiTiet = state.phieuNhapXuat.dsNhapXuatChiTiet || [];
      dispatch.phieuNhapXuat.updateData({
        dsNhapXuatChiTiet: [
          ...dsNhapXuatChiTiet,
          {
            ...item,
            soLuongSoCap: 0,
            moi: true,
            index: dsNhapXuatChiTiet.length + 1,
          },
        ],
      });
    },
    onSelectItem: ({ item }, state) => {
      const dsNhapXuatChiTiet = state.phieuNhapXuat.dsNhapXuatChiTiet || [];
      dispatch.phieuNhapXuat.updateData({
        dsNhapXuatChiTiet: [
          ...dsNhapXuatChiTiet,
          {
            ...item,
            soLuong: item.soLuongKhaDung,
            index: dsNhapXuatChiTiet.length + 1,
          },
        ],
      });
    },
    onSelectMultiItem: async ({ data = [], loNhap }, state) => {
      const dsNhapXuatChiTiet = state.phieuNhapXuat.dsNhapXuatChiTiet || [];
      dsNhapXuatChiTiet.forEach((item) => {
        for (let i = 0; i < data.length; i++) {
          if (item.id && data[i].id && data[i].id === item.id) {
            item.soLuongSoCapYeuCau = (item.soLuongSoCapYeuCau || 0) + 1;
            item.soLuongSoCap = item.soLuongSoCapYeuCau;
            item.soLuongYeuCau = (item.soLuongYeuCau || 0) + 1;
            item.soLuong = item.soLuongYeuCau;
            data.splice(i, 1);
            break;
          }
        }
      });
      let res = [];
      if (loNhap) {
        const khoHienTai = state.kho.currentItem;
        const list = await dispatch.thangSoBanLe.getListAllThangSoBanLe({
          active: true,
          page: "",
          size: "",
        });

        res = list.filter((x) => x.khoId === khoHienTai?.id);
      }
      dispatch.phieuNhapXuat.updateData({
        dsNhapXuatChiTiet: [
          ...dsNhapXuatChiTiet,
          ...data.map((item, index) => {
            if (loNhap) {
              res = res.filter(
                (x) =>
                  (!x.phanLoaiDvKhoId ||
                    x.phanLoaiDvKhoId === item.phanLoaiDvKhoId) &&
                  (!x.nhomDichVuCap2Id ||
                    x.nhomDichVuCap2Id === item.nhomDichVuCap2Id) &&
                  (!x.nhomDichVuCap1Id ||
                    x.nhomDichVuCap1Id === item.nhomDichVuCap1Id)
              );
              res = res.filter((x) => {
                const min = x?.giaNhapNhoNhat || 0;
                const max = x?.giaNhapLonNhat || Number.MAX_SAFE_INTEGER;
                const giaNhapSauVat = item?.giaNhapSauVat || 0;

                return min <= giaNhapSauVat && giaNhapSauVat <= max;
              });

              delete item.soLuong;
            }
            let thangSoBanLe = loNhap ? getThangSoBanLe(res) : null;

            const giaNhapTruocVat = (
              (item?.giaNhapSauVat || 0) /
              (1 + (item?.vat || 5) / 100)
            ).toFixed(3);

            return {
              ...item,
              key: `${item.dichVuId}_${stringUtils.guid()}`,
              ...(loNhap && {
                detachId: stringUtils.guid(),
                loaiChietKhau: LOAI_CHIET_KHAU.PHAN_TRAM,
                soLuongSoCap: 0,
                loNhapId: null,
                loNhap: {
                  giaBaoHiem: item.giaBaoHiem || 0,
                  giaNhapSauVat: item.giaNhapSauVat || 0,
                  giaNhapTruocVat: giaNhapTruocVat || 0,
                  thangSoBanLe,
                  vat: item.vat || 5,
                  vatBan: item.data,
                  xuatXuId: item.xuatXuId,
                  giaPhuThu: item.giaPhuThu || 0,
                  giaKhongBaoHiem: item.giaKhongBaoHiem || 0,
                  dvtSoCapId: item.dvtSoCapId,
                  nhaSanXuatId: item.nhaSanXuatId,
                  kichCoVtId: item.kichCoVtId,
                  soLo: item.soLo,
                  ngayHanSuDung: item.ngayHanSuDung,
                },
              }),
              index: dsNhapXuatChiTiet.length + 1 + index,
              vatTuTaiSuDung: false,
              moi: true,
              id: null,
            };
          }),
        ],
      });
    },
    onRemoveItem: ({ item }, state) => {
      const thongTinPhieu = state.phieuNhapXuat.thongTinPhieu || {};
      let dsNhapXuatChiTiet = state.phieuNhapXuat.dsNhapXuatChiTiet || [];
      if ([10, 15].includes(thongTinPhieu.trangThai || 10)) {
        dsNhapXuatChiTiet = dsNhapXuatChiTiet
          .filter((it) => it.id != item?.id)
          .map((item, index) => {
            item.index = index + 1;
            return item;
          });
        dispatch.phieuNhapXuat.updateData({
          dsNhapXuatChiTiet,
        });
      }
    },
    onSizeChange: ({ dataSearch, ...rest }) => {
      dispatch.phieuNhapXuat.updateData({
        page: 0,
        dataSearch,
        ...rest,
      });
      dispatch.phieuNhapXuat.onSearch({ ...rest });
    },
    onSortChange: ({ ...payload }, state) => {
      const dataSortColumn = {
        ...state.phieuNhapXuat.dataSortColumn,
        ...payload,
      };
      dispatch.phieuNhapXuat.updateData({
        page: 0,
        dataSortColumn,
      });
      dispatch.phieuNhapXuat.onSearch({
        page: 0,
        dataSortColumn,
      });
    },
    onSearch: ({ page = 0, size = 20, ...payload }, state) => {
      return new Promise(async (resolve, reject) => {
        try {
          let timKiem = payload.dataSearch?.timKiem;
          const dataSortColumn = payload.dataSortColumn || {};

          const thongTinPhieu = state.phieuNhapXuat.thongTinPhieu || {};

          const dsNhapXuatChiTietFull = thongTinPhieu?.id
            ? state.phieuNhapXuat.dsNhapXuatChiTietFull || []
            : state.phieuNhapXuat.dsNhapXuatChiTiet || [];

          let dsNhapXuatChiTiet = Object.keys(dataSortColumn).length
            ? cloneDeep(dsNhapXuatChiTietFull).sort((a, b) => {
                const keys = Object.keys(dataSortColumn);
                for (let i = 0; i < keys.length; i++) {
                  const key = keys[i];
                  if (dataSortColumn[key] == 0) continue;
                  const a1 = get(a, key);
                  const b1 = get(b, key);
                  if (a1 != b1) {
                    return dataSortColumn[key] == 1
                      ? b1 > a1
                        ? 1
                        : -1
                      : a1 > b1
                      ? 1
                      : -1;
                  }
                }
                return 0;
              })
            : dsNhapXuatChiTietFull;

          dsNhapXuatChiTiet = dsNhapXuatChiTiet.filter((item) => {
            return (
              containText(item.dichVu?.ten, timKiem) ||
              containText(item.dichVu?.ma, timKiem)
            );
          });
          const totalElements = dsNhapXuatChiTiet.length;

          dsNhapXuatChiTiet = dsNhapXuatChiTiet.slice(
            page * size,
            (page + 1) * size
          );
          dispatch.phieuNhapXuat.updateData({
            dsNhapXuatChiTiet,
            totalElements,
            page,
            size,
          });
          resolve(dsNhapXuatChiTiet);
        } catch (error) {
          message.error(error?.message?.toString());
          reject(error);
        }
      });
    },
    suaSoLuongDuyet: (id, state, updateDsNhapXuatChiTiet = null) => {
      return new Promise(async (resolve, reject) => {
        let data = await dispatch.phieuNhapXuat.getDataPhieuXuat({ id });
        let dsNhapXuatChiTiet = updateDsNhapXuatChiTiet
          ? updateDsNhapXuatChiTiet
          : (data.dsNhapXuatChiTiet || []).map((item) => {
              return {
                id: item.id,
                soLuongSoCap: item.moi ? item.soLuongSoCap : item.soLuong,
                loNhapId: item.loNhapId,
                // soLuong: !item.moi ? item.soLuong : null,
              };
            });
        phieuNhapXuatProvider
          .suaSoLuongDuyet(id, dsNhapXuatChiTiet)
          .then((res) => {
            if (res && res.code === 0) {
              message.success(t("kho.suaSoLuongDuyetThanhCong"));
              dispatch.phieuNhapXuat.updateData({
                thongTinPhieu: res.data,
              });
              resolve(res?.data);
            } else {
              reject(res);
            }
          })
          .catch((res) => {
            message.error(res?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(res);
          });
      }).catch((e) => {
        message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
        reject(e);
      });
    },
    xuatPhieuNhapXuat: (payload = {}, state) => {
      return new Promise((resolve, reject) => {
        phieuNhapXuatProvider
          .inPhieuNhapXuat(payload)
          .then((s) => {
            resolve(s?.data);
          })
          .catch((err) => {
            message.error(err?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(err);
          });
      });
    },
    getListPhieu: (payload = {}, state) => {
      return new Promise((resolve, reject) => {
        phieuNhapXuatProvider.searchAllByPut({ ...payload }).then((s) => {
          const { totalElements, page, size } = s;
          dispatch.phieuNhapXuat.updateData({
            dsPhieuNhapXuat: s.data,
            totalElements,
            page,
            size,
          });
          resolve(s?.data);
        });
      });
    },
    patch: ({ ...payload }, state) => {
      return new Promise(async (resolve, reject) => {
        const { nguoiDuyetId, thoiGianDuyet, ...rest } = payload;

        await dispatch.thuocChiTiet.updateThoiGianPhat({
          id: payload.id,
          thoiGianDuyet,
          nguoiDuyetId,
        });
        phieuNhapXuatProvider
          .patch(rest)
          .then((s) => {
            message.success(t("common.capNhatThanhCong"));
            dispatch.phieuNhapXuat.updateData({
              thongTinPhieu: s.data,
            });
            resolve(s.data);
          })
          .catch((res) => {
            message.error(res?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(res);
          });
      });
    },

    xoaHangHoaPhieuTra: (payload = {}) => {
      return new Promise((resolve, reject) => {
        nbDvThuocProvider
          .xoaPhieuTraKho(payload)
          .then((s) => {
            message.success(t("kho.xoaHangHoaThanhCong"));
            resolve(s);
          })
          .catch((res) => {
            message.error(res?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    inPhieuLinhChiTiet: (payload = {}, state) => {
      return new Promise((resolve, reject) => {
        const { id, dsPhongId } = payload;
        phieuNhapXuatProvider
          .inPhieuLinhChiTiet({ id, dsPhongId })
          .then(async (s) => {
            if (s.data) {
              if (payload.openInNewTab) {
                let url = "";
                if (payload.printMerge) {
                  url = await printProvider.getMergePdf([s.data?.file?.pdf]);
                } else {
                  let urlFileLocal = await fileUtils.getFromUrl({
                    url: fileUtils.absoluteFileUrl(s.data.file.pdf),
                  });
                  const blob = new Blob([new Uint8Array(urlFileLocal)], {
                    type: "application/pdf",
                  });
                  url = window.URL.createObjectURL(blob);
                }
                openInNewTab(url);
                resolve(url);
              } else {
                if (payload.printMerge)
                  printProvider.printMergePdf([s.data?.file?.pdf]);
                else printProvider.printPdf(s.data);

                resolve();
              }
            } else {
              reject();
            }
          })
          .catch((e) => {
            message.error(e?.message);
            reject();
          });
      });
    },
    inPhieuDsLinhThuoc: (params, state) => {
      return new Promise((resolve, reject) => {
        phieuNhapXuatProvider
          .inPhieuDsLinhThuoc(params)
          .then((s) => {
            if (s.data) {
              printProvider.printPdf(s.data);
              resolve(s);
            } else {
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
              reject(s);
            }
          })
          .catch((res) => {
            message.error(res?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(res);
          });
      });
    },
    inPhieuDsLinhVatTu: (phieuLinhId, state) => {
      return new Promise((resolve, reject) => {
        phieuNhapXuatProvider
          .inPhieuDsLinhVatTu(phieuLinhId)
          .then((s) => {
            if (s.data) {
              printProvider.printPdf(s.data);
              resolve(s);
            } else {
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
              reject(s);
            }
          })
          .catch((res) => {
            message.error(res?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(res);
          });
      });
    },
    inPhieuLinhBuKichCo: (id) => {
      return new Promise((resolve, reject) => {
        phieuNhapXuatProvider
          .inPhieuLinhBuKichCo(id)
          .then((s) => {
            if (s.data) {
              printProvider.printPdf(s.data);
              resolve(s);
            } else {
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
              reject(s);
            }
          })
          .catch((res) => {
            message.error(res?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(res);
          });
      });
    },
    taiLenFile: ({ id, file, ...payload }) => {
      return new Promise(async (resolve, reject) => {
        let files = [];
        files = file.map((item) => {
          if (item.type !== "application/pdf") {
            return dispatch.hoSoBenhAn.imageToPdf(item);
          } else {
            return item;
          }
        });
        const fileUpload = await Promise.all(files);

        phieuNhapXuatProvider
          .taiLenFile({ id, file: fileUpload, ...payload })
          .then((s) => {
            resolve(s.data);
            message.success(t("common.themMoiThanhCongDuLieu"));
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    patchDsChungTu: (payload, state) => {
      return new Promise(async (resolve, reject) => {
        phieuNhapXuatProvider
          .patch(payload)
          .then((s) => {
            message.success(t("common.capNhatThanhCong"));
            dispatch.phieuNhapXuat.updateData({
              thongTinPhieu: s.data,
            });
            resolve(s.data);
          })
          .catch((res) => {
            message.error(res?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(res);
          });
      });
    },
    inTemThuocNCC: (payload = {}, state) => {
      return new Promise((resolve, reject) => {
        phieuNhapXuatProvider
          .inTemThuocNCC(payload)
          .then((s) => {
            if (s.data) {
              printProvider.printPdf(s.data);
            }
            resolve(s);
          })
          .catch((e) => {
            reject(e);
          });
      });
    },
    inPhieuCongKhaiThuoc: (id) => {
      return new Promise((resolve, reject) => {
        phieuNhapXuatProvider
          .inPhieuCongKhaiThuoc(id)
          .then(async (s) => {
            if (s.data?.length > 0) {
              const finalFile = await printProvider.getMergePdf(
                s.data.map((item) => item.file.pdf)
              );
              openInNewTab(finalFile);
              resolve(finalFile);
            } else {
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
              reject(s);
            }
          })
          .catch((res) => {
            message.error(res?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(res);
          });
      });
    },
    inBienBanKiemNhap: (payload) => {
      return new Promise((resolve, reject) => {
        phieuNhapXuatProvider
          .inBienBanKiemNhap(payload)
          .then(async (s) => {
            if (s?.data) {
              resolve(s?.data);
            }
          })
          .catch((e) => {
            reject();
          });
      });
    },
    inPhieuLinhThuocDieuTriNgoaiTru: (payload) => {
      return new Promise((resolve, reject) => {
        phieuNhapXuatProvider
          .inPhieuLinhThuocDieuTriNgoaiTru(payload)
          .then(async (s) => {
            if (s?.data) {
              printProvider.printPdf(s.data);
            }
            resolve(s);
          })
          .catch((e) => {
            reject();
          });
      });
    },
    duyetNhapPhieu: (payload = {}, state) => {
      return new Promise((resolve, reject) => {
        const { id } = payload;
        phieuNhapXuatProvider
          .duyetNhapPhieu({ id })
          .then((res) => {
            if (res && res.code === 0) {
              message.success(t("kho.duyetThanhCong"));
              dispatch.phieuNhapXuat.getById(id);
              resolve(res);
            } else {
              message.error(
                res?.message || t("common.xayRaLoiVuiLongThuLaiSau")
              );
              reject(res);
            }
          })
          .catch((res) => {
            message.error(res?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(res);
          });
      });
    },
    xuatPhieuLinh: (payload = {}) => {
      return new Promise((resolve, reject) => {
        const { id, tachLo } = payload;
        const params = { id };
        if (tachLo === true) {
          params.tachLo = true;
        }
        phieuNhapXuatProvider
          .inPhieuLinh(params)
          .then((s) => {
            if (s?.data) {
              resolve(s.data);
            } else {
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
              reject(s);
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    xuatPhieuLinhChiTiet: (payload = {}, state) => {
      return new Promise((resolve, reject) => {
        const { id } = payload;
        phieuNhapXuatProvider
          .inPhieuLinhChiTiet({ id })
          .then(async (s) => {
            if (s.data) {
              resolve(s.data);
            } else {
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
              reject(s);
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    inPhieuBienBanGiaoNhan: (payload) => {
      return new Promise((resolve, reject) => {
        phieuNhapXuatProvider
          .getBienBanGiaoNhan(payload)
          .then(async (s) => {
            if (s?.data) {
              printProvider.printPdf(s.data);
            }
            resolve(s);
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    inPhieuXuatHuy: (payload) => {
      return new Promise((resolve, reject) => {
        phieuNhapXuatProvider
          .getPhieuXuatHuy(payload)
          .then(async (s) => {
            if (s?.data) {
              printProvider.printPdf(s.data);
            }
            resolve(s);
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    thanhToanNhaCungCap: (payload, state) => {
      return new Promise((resolve, reject) => {
        phieuNhapXuatProvider
          .thanhToanNhaCungCap(payload)
          .then((res) => {
            dispatch.phieuNhapXuat.getById(payload);
            message.success(t("thuNgan.thanhToanThanhCong"));
            resolve(res);
          })
          .catch((res) => {
            message.error(res?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(res);
          });
      });
    },
    huyThanhToanNhaCungCap: (payload, state) => {
      return new Promise((resolve, reject) => {
        phieuNhapXuatProvider
          .huyThanhToanNhaCungCap(payload)
          .then((res) => {
            dispatch.phieuNhapXuat.getById(payload);
            message.success(t("thuNgan.thanhToanThanhCong"));
            resolve(res);
          })
          .catch((res) => {
            message.error(res?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(res);
          });
      });
    },
    chuyenPhieuLinhBu: (payload, state) => {
      return new Promise((resolve, reject) => {
        phieuNhapXuatProvider
          .chuyenPhieuLinhBu(payload)
          .then((res) => {
            message.success(t("quanLyNoiTru.chuyenPhieuThanhCong"));
            resolve(res);
          })
          .catch((res) => {
            message.error(res?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(res);
          });
      });
    },
    inPhieuBienBanThanhLy: (payload) => {
      return new Promise((resolve, reject) => {
        phieuNhapXuatProvider
          .getBienBanThanhLy(payload)
          .then(async (s) => {
            if (s?.data) {
              printProvider.printPdf(s.data);
            }
            resolve(s);
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(e);
          });
      });
    },
    inPhieuDsNbLinhThuoc: (params, state) => {
      return new Promise((resolve, reject) => {
        phieuNhapXuatProvider
          .inPhieuDsNbLinhThuoc(params)
          .then((s) => {
            if (s.data) {
              printProvider.printPdf(s.data);
              resolve(s);
            } else {
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
              reject(s);
            }
          })
          .catch((res) => {
            message.error(res?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(res);
          });
      });
    },
    updateKhoPhieuNhapXuat: ({ ...rest }, state) => {
      return new Promise((resolve, reject) => {
        phieuNhapXuatProvider
          .updateKhoPhieuNhapXuat(rest)
          .then((s) => {
            if (s?.code === 0) {
              message.success(t("khoMau.doiKhoPhatMauThanhCong"));
              resolve(s.data);
            } else {
              reject(s);
            }
          })
          .catch((e) => {
            message.error(e?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
          });
      });
    },
    inPhieuLinhMau: (payload = {}, state) => {
      return new Promise((resolve, reject) => {
        const { id } = payload;

        phieuNhapXuatProvider
          .inPhieuLinhMau({ id })
          .then(async (s) => {
            if (s.data) {
              if (payload.openInNewTab) {
                let url = "";
                if (payload.printMerge) {
                  url = await printProvider.getMergePdf([s.data?.file?.pdf]);
                } else {
                  let urlFileLocal = await fileUtils.getFromUrl({
                    url: fileUtils.absoluteFileUrl(s.data.file.pdf),
                  });
                  const blob = new Blob([new Uint8Array(urlFileLocal)], {
                    type: "application/pdf",
                  });
                  url = window.URL.createObjectURL(blob);
                }
                openInNewTab(url);
                resolve(url);
              } else {
                if (payload.printMerge) {
                  printProvider.printMergePdf([s.data?.file?.pdf]);
                } else {
                  printProvider.printPdf(s.data);
                }
                resolve();
              }
            } else {
              reject();
            }
          })
          .catch((e) => {
            message.error(e?.message);
            reject();
          });
      });
    },
    inTemDuTruMau: (params, state) => {
      return new Promise((resolve, reject) => {
        phieuNhapXuatProvider
          .inTemDuTruMau(params)
          .then((s) => {
            if (s.data) {
              printProvider.printPdf(s.data);
              resolve(s);
            } else {
              message.error(s?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
              reject(s);
            }
          })
          .catch((res) => {
            message.error(res?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(res);
          });
      });
    },
    huyDuyetNhapPhieu: (payload = {}, state) => {
      return new Promise((resolve, reject) => {
        const { id } = payload;
        phieuNhapXuatProvider
          .huyDuyetNhapPhieu({ id })
          .then((res) => {
            if (res && res.code === 0) {
              message.success(t("kho.huyDuyetThanhCong"));
              dispatch.phieuNhapXuat.getById(id);
              resolve(res);
            } else {
              message.error(
                res?.message || t("common.xayRaLoiVuiLongThuLaiSau")
              );
              reject(res);
            }
          })
          .catch((res) => {
            message.error(res?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(res);
          });
      });
    },
    soanDonPhieuNhapXuat: (payload, state) => {
      return new Promise((resolve, reject) => {
        phieuNhapXuatProvider
          .soanDonPhieuNhapXuat(payload)
          .then((res) => {
            message.success(t("kho.soanDonThanhCong"));
            dispatch.phieuNhapXuat.updateData({
              thongTinPhieu: res.data,
            });
            resolve(res);
          })
          .catch((res) => {
            message.error(res?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(res);
          });
      });
    },
    huySoanDonPhieuNhapXuat: (payload = {}, state) => {
      return new Promise((resolve, reject) => {
        phieuNhapXuatProvider
          .huySoanDonPhieuNhapXuat(payload)
          .then((res) => {
            message.success(t("kho.huySoanDonThanhCong"));
            dispatch.phieuNhapXuat.updateData({
              thongTinPhieu: res.data,
            });
            resolve(res);
          })
          .catch((res) => {
            message.error(res?.message || t("common.xayRaLoiVuiLongThuLaiSau"));
            reject(res);
          });
      });
    },
    inTemThuocVat: (payload = {}, state) => {
      return new Promise((resolve, reject) => {
        phieuNhapXuatProvider
          .getTemThuoc(payload)
          .then((s) => {
            if (s.data) {
              printProvider.printPdf(s.data);
            }
            resolve(s);
          })
          .catch((e) => {
            reject(e);
          });
      });
    },
  }),
};
