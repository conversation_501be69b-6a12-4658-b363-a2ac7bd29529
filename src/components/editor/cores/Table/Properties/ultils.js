import { t } from "i18next";
import { LOAI_DICH_VU } from "constants/index";

export const LIST_PHIEU_DICH_TRUYEN = [
  {
    ten: t("editor.stt"),
    fieldName: "stt",
    show: false,
    rowSpan: 2,
    width: 30,
    en: "No.",
  },
  {
    ten: t("editor.ngayThang"),
    fieldName: "ngayThang",
    show: true,
    rowSpan: 2,
    width: 50,
    en: "Date",
  },
  {
    ten: t("editor.tenDichTruyenHamLuong"),
    fieldName: "tenDichTruyenHamLuong",
    show: true,
    rowSpan: 2,
    en: "Name of IV fluid / Concentration",
  },
  {
    ten: t("editor.tenDonViTinh"),
    fieldName: "tenDonViTinh",
    show: false,
    rowSpan: 2,
    width: 50,
    en: "Unit",
  },
  {
    ten: t("editor.soLuong"),
    fieldName: "soLuong",
    show: true,
    rowSpan: 2,
    width: 70,
    en: "Quantity",
  },
  {
    ten: t("editor.cachDung"),
    fieldName: "cachDung",
    show: false,
    rowSpan: 2,
    width: 120,
    en: "Use",
  },
  {
    ten: t("editor.loSoSanXuat"),
    fieldName: "loSoSanXuat",
    show: true,
    rowSpan: 2,
    width: 50,
    en: "Batch number / Lot number",
  },
  {
    ten: t("editor.tocDo"),
    fieldName: "tocDo",
    show: true,
    rowSpan: 2,
    width: 80,
    en: "Drip rate (drops/min)",
  },
  {
    ten: t("editor.thoiGian"),
    fieldName: "thoiGian",
    show: true,
    colSpan: 2,
    width: 80,
    en: "Time",
  },
  {
    ten: t("editor.bsChiDinh"),
    fieldName: "bsChiDinh",
    show: true,
    rowSpan: 2,
    width: 90,
    en: "Physician",
  },
  {
    ten: t("editor.ddThucHien"),
    fieldName: "ddThucHien",
    show: true,
    rowSpan: 2,
    width: 70,
    en: "Attending nurse",
  },
  {
    ten: t("editor.ghiChu"),
    fieldName: "ghiChu",
    show: true,
    rowSpan: 2,
    width: 80,
    en: "Note",
  },
  {
    ten: t("editor.tongKetDichTruyen"),
    fieldName: "tongKetDichTruyen",
    show: false,
    rowSpan: 2,
    width: 120,
    en: "Summary of transfusion",
  },
];

export const LIST_PHIEU_TONG_HOP_THU_THUAT_HUT_HOM = [
  {
    ten: t("editor.ngay"),
    fieldName: "thoiGian",
    show: true,
    colSpan: 1,
    width: 52,
  },
  {
    ten: t("editor.hutDomQuaOngNoiKhiQuan"),
    fieldName: "hutDomQuaOngNoiKhiQuan",
    show: true,
    colSpan: 5,
    width: 100,
  },
  {
    ten: t("editor.hutDomHauHong"),
    fieldName: "hutDomHauHong",
    show: true,
    colSpan: 1,
    width: 45,
  },
  {
    ten: t("editor.anChePhamDinhDuong"),
    fieldName: "chePhamDinhDuong",
    show: true,
    colSpan: 1,
    width: 65,
  },
  {
    ten: t("editor.lyDoKhongAn"),
    fieldName: "lyDoKhongAn",
    show: true,
    rowSpan: 2,
    width: 60,
  },
  {
    ten: t("editor.nguoiThucHien"),
    fieldName: "nguoiThucHien",
    show: true,
    rowSpan: 2,
    width: 60,
  },
];

export const TABLE_TYPES = [
  { value: "normal", label: "Normal" },
  { value: "gridData", label: "Grid data" },
  { value: "insertRow", label: "Insert row" },
  { value: "replicationRow", label: "Replication row" },
  { value: "vat-tu-tieu-hao", label: "Vật tư tiêu hao" },
  { value: "tien-su-di-ung", label: "Tiền sử dị ứng" },
  { value: "tom-tat-benh-an", label: "Tóm tắt bệnh án" },
  { value: "phieu-truyen-dich", label: "Phiếu truyền dịch" },
  {
    value: "phieu-tong-hop-thu-thuat-hut-dom",
    label: "Phiếu tổng hợp thủ thuật hút đờm - dinh dưỡng",
  },
  { value: "listRender", label: "List render" },
  { value: "ban-giao-thuoc", label: "Bàn giao thuốc" },
  {
    value: "cong-khai-dich-vu-kham",
    label: "Phiếu công khai dịch vụ khám, chữa bệnh nội trú",
  },
  {
    value: "thuc-hien-va-cong-khai-thuoc",
    label: "Phiếu thực hiện và công khai thuốc",
  },
  {
    value: "thuc-hien-va-cong-khai-thuoc-dktd",
    label: "Phiếu thực hiện và công khai thuốc BVĐKKVTĐ",
  },
  {
    value: "thuc-hien-va-cong-khai-vtyt",
    label: "Phiếu thực hiện và công khai vật tư y tế tiêu hao",
  },
  { value: "theo-doi-mang-bung", label: "Bảng theo dõi màng bụng 24h" },
  { value: "kiem-y-dung-cu", label: "Bảng kiểm y - dụng cụ phòng mổ" },
  { value: "kiem-diem-dem-dung-cu", label: "Bảng Kiểm đếm dụng cụ" },
  { value: "phieu-su-dung-thuoc", label: "Phiếu sử dụng thuốc" },
  { value: "dsnv-giay-moi-hoi-chan", label: "DSNV Giấy mời hội chẩn" },
  { value: "phieu-theo-doi-nb-sau-mo", label: "Phiếu theo dõi NB sau mổ" },
  {
    value: "phieu-theo-doi-dieu-tri-than-nhan-tao",
    label: "Phiếu theo dõi và điều trị thận nhân tạo",
  },
  {
    value: "phieu-theo-doi-dieu-tri-than-nhan-tao-dd",
    label: "Phiếu theo dõi và điều trị thận nhân tạo (Đống Đa)",
  },
  {
    value: "phieu-theo-doi-hoi-suc-nb-chua-mo",
    label: "Phiếu theo dõi và hồi sức người bệnh chưa mổ và sau mổ",
  },
  {
    value: "phieu-cham-soc-nguoi-benh-sau-thu-thuat-pha-thai",
    label: "Phiếu chăm sóc người bệnh sau thủ thuật phá thai",
  },
  {
    value: "bang-theo-doi-tang-truong",
    label: "Bảng theo dõi tăng trưởng bé gái",
  },
  { value: "cac-chi-so-sinh-ton", label: "Các chỉ số sinh tồn" },
  { value: "bang-cham-diem-benh-an", label: "Bảng chấm điểm bệnh án" },
  {
    value: "bang-tu-van-dang-ky-dv-tu-nguyen",
    label: "Bảng tư vấn đăng ký DV tự nguyện",
  },
  { value: "phieu-theo-doi-tre-so-sinh", label: "Phiếu theo dõi trẻ sơ sinh" },
  {
    value: "phieu-ban-giao-thuoc-cho-bn",
    label: "Phiếu bàn giao thuốc cho bệnh nhân",
  },
];

// Default tool names
export const DEFAULT_DUNG_CU = [
  "Gạc phẫu thuật 7x11x12 lớp vô trùng",
  "Gạc phẫu thuật không dệt 7.5x7, 5x6 lớp vô trùng",
  "Gạc phẫu thuật ổ bụng 30x40x6 lớp vô trùng",
  "Gạc Mecher vô trùng",
  "Kim",
  "Lưỡi dao",
  "Dụng cụ",
];

//Default chỉ định từ loại DV
export const DEFAULT_CHI_DINH_TU_LOAI_DV = {
  KHAM_BENH: [LOAI_DICH_VU.KHAM],
  NOI_TRU: [LOAI_DICH_VU.TO_DIEU_TRI],
  PTTT: [LOAI_DICH_VU.PHAU_THUAT_THU_THUAT],
};

export const TEN_MAN_HINH = {
  KHAM_BENH: "Khám bệnh",
  NOI_TRU: "Tờ điều trị",
  PTTT: "PTTT ",
};
