{"nguoiDuyetPhat": "Approver for distribution", "lyDo": "Reason", "thanhTien": "Total amount", "khoXuat": "Export warehouse", "soPhieuNhap": "Import Note No.", "soHoaDon": "Invoice Number", "nguoiTaoPhieu": "Note Creator", "nguoiTao": "Creator", "ghiChu": "Note", "nhaCungCap": "Provider", "diaChiNcc": "Supplier address", "loaiXuat": "Export type", "khoNhap": "Import Warehouse", "thangDuTru": "Estimated month", "soPhieu": "Delivery No.", "kho": "Warehouse", "tenKho": "Warehouse name", "chonKho": "Select warehouse", "xuatKho": "Export from warehouse", "keTaiKho": "Listing in warehouse", "lanTra": "Return times", "slTra": "Quantity of return", "slYeuCau": "Requested quantities", "slYeuCauTra": "Requested quantities for return", "slSoCapYeuCauTra": "Requested primary quantities for return", "ngayTra": "Date of return", "thongTinPhieuNhap": "Information of input note", "quetDinhThau": "Bidding decision", "vuiLongChonNhaCungCap": "Please select a supplier", "chonNhaCungCap": "Select provider", "nguonNhapKho": "Input source of warehouse", "vuiLongChonNguonNhapKho": "Please select import source of warehouse", "hinhThucNhap": "Import type", "chonHinhThucNhap": "Select import type", "khoaTaoPhieu": "Deparment creating the note", "vuiLongChonHinhThucNhap": "Please select input type", "thoiGianDuyet": "Approval time", "vuiLongChonThoiGianDuyet": "Please approval time", "vuiLongNhapSoHoaDon": "Please enter invoice number", "ngayHoaDon": "Invoice date", "vuiLongChonNgayHoaDon": "Please select invoice date", "kyHieuHoaDon": "Invoice symbol", "vuiLongNhapKyHieuHoaDon": "Please enter invoice symbol", "soHopDong": "Contract number", "vuiLongNhapSoHopDong": "Please enter contract number", "vuiLongNhapGhiChu": "Please enter a note", "nguoiLapPhieu": "Person creating the note", "soLuongVuotQuaTonKhaDung": "Quantity exceeds availability", "tenHangHoa": "Name of goods", "vuiLongChonHangHoa": "Please select goods", "nhapGhiChu": "Enter note", "hamLuong": "Content", "slDuTru": "Estimated quantity", "slDuyet": "Approved quantity", "soLo": "Lot number", "hanSuDung": "Expiry date", "phieuNhapDuTru": "Expected entry form", "luuVaGuiDuyetPhieu": "Save and send form", "luuPhieu": "Save form", "scanChungTu": "Scan Document", "huy": "Cancel", "nhapKho": "Warehouse import", "boLoc": "Filter", "xemTongHop": "View summary", "xemTheoLo": "View by lot", "xemTheoQuyetDinhThau": "View by bidding decision", "xemTheoLanNhap": "View by entry", "tatCaKho": "All warehouses", "tatCa": "All", "danhSachTonKho": "Inventory list", "donViTinh": "Unit of measurement", "loaiDichVu": "Type of service", "slTonThucTe": "Actual Inventory Quantity", "nhapSlNhoNhat": "Enter the smallest quantity", "nhapSlLonNhat": "Enter the largest quantity", "slTonKhaDung": "Available stock", "tim": "Search", "maHangHoa": "Code of goods", "nhapMaHangHoa": "Enter code of goods", "nhapTenHangHoa": "Enter name of goods", "slTonThucTeSoCap": "Actual (primary) inventory stock", "slTonThucTeThuCap": "Actual (secondary) inventory stock", "slTonKhaDungSoCap": "Available (primary) inventory stock", "slTonKhaDungThuCap": "Available secondary) inventory stock", "slGiuChoSoCap": "Reserved (primary) quantity", "slGiuChoThuCap": "Reserved (secondary) quantity", "tongSlNhap": "Total import quantity", "slThucXuat": "Actual export quantity", "dvtSoCap": "Unit of Measurement (primary)", "dvtThuCap": "Unit of Measurement (secondary)", "qdThau": "Bidding decision", "hieuLucThau": "Bidding validity", "hoatChat": "Active ingredients", "tenHoatChat": "Active ingredient name", "nhapTenHoatChat": "Enter the name of the active ingredient", "phanLoaiThuoc": "Medicine classification", "nhapPhanLoaiThuoc": "Enter medication classification", "phatThuocNgoaiTru": "Distributing outpatient medicines", "thongTinDonThuoc": "Prescription information", "quetQrNguoiBenhHoacNhapMaHoSoDeTimKiem": "Scan patient QR code or enter patient file code to search", "coHieuLuc": "<PERSON><PERSON>", "trangThai": "Status", "khoa": "Department", "vuiLongChonKhoa": "Please select the department!", "chonHieuLuc": "Select validity", "timTrangThai": "Search status", "timKhoa": "Search department", "quyetDinhThau": {"title": "Bidding decision", "banCoChacChanHoanThanhQuyetDinhThau": "Are you sure to complete the bidding decision?", "banCoChacChanHuyHoanThanhQuyetDinhThau": "Are you sure to cancel the bidding decision?", "banCoChacChanDuyetQuyetDinhThau": "Are you sure to approve the bidding decision?", "banCoChacChanHuyDuyetQuyetDinhThau": "Are you sure to cancel the approval of the bidding decision?", "huyHoanThanh": "Cancel completion", "hoanThanh": "Completed", "duyet": "Approve", "huyDuyet": "Cancel approval", "thongTinChiTiet": "Detailed information", "nam": "Year", "vuiLongChonNam": "Please select year!", "vuiLongNhapNam": "Please enter year", "vuiLongNhapQuyetDinhThau": "Please enter bidding decision", "tenGoiThau": "Bidding package name", "vuiLongNhapGoiThau": "Please enter bidding package", "vuiLongChonLoaiDichVu": "Please select service type", "vuiLongChonHieuLucThau": "Please select bidding validity", "hieuLucThau": "Bidding validity", "vuiLongChonNgayCongBo": "Please select a publication date", "ngayCongBo": "Publication date", "loaiThau": "Bidding type", "vuiLongChonLoaiThau": "Please select bidding type", "loaiDv": "Type of service", "timNam": "Search year", "timQuyetDinhThau": "Search biding decision", "timGoiThau": "Search bidding packages", "timLoaiDichVu": "Search service type", "timNguonNhapKho": "Search import source of warehouse", "timLoaiThau": "Search bidding type", "timNgayCongBo": "Search publication date", "timHieuLucThau": "Search bidding validity", "khongDuocPhepNhapSlLonHon120%SlThau": "Cannot enter quantity > 120% of the bidding quantity", "tiLeThanhToanBhDonGiaBang0": "Insurance payment rate = 0. Insured unit price = 0", "tiLeThanhToanBhLonHon0DonGiaBhBangDonGiaSauVat": "Insurance payment rate> 0. Insured unit price = Unit price after VAT", "chiDuocSuaKhiQuyetDinhThauChuaHoanThanh": "Only correct when the Bidding Decision has not been completed", "chonThauTruocKhiChonHangHoa": "Choose a bid before selecting goods", "maHangHoaTrungThau": "The code of goods won the bid", "vuiLongNhapMaTrungThau": "Please enter the winning code of the bid", "tenHangHoaTrungThau": "Name of the winning goods", "vuiLongNhapTenTrungThau": "Please enter the winning name", "maHangHoaDauThau": "Bidding code of goods", "vuiLongNhapMaDauThau": "Please enter bidding code of goods", "vuiLongNhapSlThau": "Please enter bidding quantity", "slThau": "Bidding quantity", "slDuocPhepMua": "Quantity allowed to buy", "vuiLongNhapSlDuocPhepMua": "Please enter quantity allowed to buy", "giaNhapSauVat": "Import price after VAT", "vuiLongNhapGiaSauVat": "Please enter the import price after VAT", "donGiaKhongBh": "Uninsured unit price", "vuiLongNhapGiaKhongBh": "Please enter uninsured unit price", "donGiaBh": "Insured unit price", "vuiLongNhapGiaBh": "Please enter insured unit price", "phuThu": "Surcharge", "vuiLongNhapGiaPhuThu": "Please enter surcharge price", "quyCach": "Specification", "vuiLongNhapQuyCach": "Please enter specification", "nhaCungCap": "Provider", "vuiLongChonNhaCungCap": "Please select a supplier", "maGoiThau": "Bidding package code", "vuiLongChonMaGoiThau": "Please select bidding package code", "soVisa": "Visa number", "vuiLongNhapSoVisa": "Please enter visa number", "nhomThau": "Contracting group", "vuiLongChonNhomThau": "Please select bidding group", "nhomChiPhi": "Cost group", "vuiLongChonNhomChiPhi": "Please select cost group", "tyLeThanhToanBh": "Insurance payment rate", "vuiLongNhapTyLeThanhToanBh": "Please enter insurance payment rate", "nguongThau": "Bidding threshold", "vuiLongNhapNguongThau": "Please enter bidding threshold", "loaiThuoc": "Type of medication", "vuiLongChonLoaiThuoc": "Please select medication", "xuatXu": "Country of origin", "vuiLongChonXuatXu": "Please select country of origin", "vuiLongChonNhaSanXuat": "Please select a manufacturer", "tranBaoHiem": "Insurance ceiling", "soHopDong": "Contract number", "vuiLongNhapSoHopDong": "Please enter contract number", "giaTran": "Price ceiling", "duongDung": "Route of administration", "maHoatChat": "Active ingredient code", "hamLuong": "Content", "maHangHoa": "Code of goods", "donViTinh": "Unit of measurement", "maAnhXa": "Mapping code", "maHieu": "Code", "nhomVatTu": "Supply group", "soLuongHangHoaDaNhap": "Number of imported goods", "soLuongDaNhap": "Amount entered", "soLuongHangHoaDaTraLaiNcc": "Number of goods returned to supplier", "soLuongHangHoaConLai": "Remaining quantity of goods", "soLuongConLai": "Remaining quantity", "timDichVu": "Search service", "timMaTrungThau": "Search winning code of the bid", "timMaHoatChat": "Search active ingredient code", "timSLConLai": "Search the remaining quantity", "timSLDaNhap": "Search the entered quantity", "timDonViTinh": "Search unit of measurement", "timTenTrungThau": "Search the winning name", "timMaDauThau": "Search bidding code", "timSoLuongThau": "Search bidding quantity", "timSlDuocPhepMua": "Search the quantity allowed for purchase", "timGiaNhapSauVat": "Search input price after VAT", "timDonGiaKhongBh": "Search uninsured unit price", "timDonGiaBh": "Search insured unit price", "timDonGiaPhuThu": "Search the surcharge unit price", "timQuyCach": "Search specification", "timNhaCungCap": "Search supplier", "timMaGoiThau": "Search bidding package code", "timSoViSa": "Search visa number", "timNhomThau": "Search bidding group", "timTyLeTTBH": "Search insurance payment rate", "timNguongThau": "Search bidding threshold", "timLoaiThuoc": "Search medication type", "timNuocSanXuat": "Search the country of manufacture", "timTranBaoHiem": "Search the insurance ceiling", "timNhaSanXuat": "Search manufacturer", "timNhomChiPhi": "Search cost group", "nuocSanXuat": "Country of manufacture", "timGiaTran": "Search ceiling price", "timSoLuongHangHoaDaNhap": "Search quantity of imported goods", "timSoLuongHangDaTraLaiNcc": "Search quanity of goods returned to supplier", "timSoLuongHangHoaConLai": "Search the remaining quantity of goods", "lyDoGiamGia": "Reason for discount", "tuNgay": "From date", "denNgay": "To date", "maAtc": "ATC code", "lyDoCoSo": "Reason", "tinhTrang": "Status", "soLuong": "Quantity", "ngayQuyetDinh": "Decision date", "donViDieuChuyen": "Unit transfer", "quyetDinhMuaThem": "Decision to buy more", "ngayHopDong": "Contract Date", "chonNgayHopDong": "Please select contract date", "ngayHetHanHopDong": "Contract expiration date", "chonNgayHetHanHopDong": "Please select the contract expiration date.", "vuiLongChonQuyetDinhThau": "Please select bidding decision", "tenTrungThau": "The winning name of the bid", "nhapTenTrungThau": "Enter the winning name of the bid", "capThau": "Bidding level", "benhVien": "Hospital", "soThau": "Bid number", "vuiLongNhapSoThau": "Please enter bid number", "vuiLongChonCapThau": "Please select bid level", "vuiLongChonBenhVien": "Please select hospital", "tinhTP": "Province/City", "vuiLongChonTinhTP": "Please select Province/City", "maSo": "Code", "vuiLongChonMaSo": "Please select the facility code", "timMaSo": "Find the base code", "congVanGiamGia": "Discount letter", "quyetDinhDieuChuyen": "Transfer decision", "giaTriMuaThem": "Additional purchase value", "vuiLongNhapGiaTriMuaThemTu0Den30": "Please enter additional purchase value from 0 to 30", "vuiLongNhapGiaTriMuaThem": "Please enter the additional purchase value.", "giaSauVatThau": "Price after VAT contractor", "tiLeThanhToanBH": "Insurance payment ratio", "tiLeThanhToanDichVu": "Payment ratio of services", "tiLeThanhToanBHDanhMuc": "Insurance payment rate (Category)", "tiLeThanhToanDichVuDanhMuc": "Service Payment Rate (Category)"}, "danhSachVtytKyGuiDaSuDung": "List of the used medical supplies for consignment", "danhSachDuyetDuocLamSang": "List of the approved clinical pharmacy", "chiTietDuyetDuocLamSang": "Details of the approved clinical pharmacy", "tatCaKhoKyGui": "All the consignment warehouses", "trangThaiHoaDon": "Invoice status", "khoaChiDinh": "Indicating department", "thoiGianThucHien": "Performance time", "tuoi": "Age", "ngaySinh": "DOB", "diaChi": "Address", "soDienThoai": "Phone number", "ma": "Code", "sl": "Quantity", "dvt": "Unit of measurement", "hoTenNb": "<PERSON><PERSON> ‘s full name", "nhapDeTimTheoMaHoacHangHoa": "Enter to search by code or goods", "phieuLinhBuTuTruc": "Duty Compensation note", "chiTietLinhBu": "Details of compensation", "inChiTietPhieu": "Print note details", "inChiTietPhieuNhap": "Print details of imput note", "xoaPhieu": "Delete receipt", "danhSachHangHoa": "List of goods", "danhSachNguoiBenhSuDung": "List of patients used", "xoaPhieuSo": "Delete note number", "thoiGianTaoPhieu": "Time of note creation", "thoiGianDuyetPhieu": "Time to note approval", "nguoiDuyet": "Approved by", "inDanhSachBenhNhan": "Print patient list", "guiDuyet": "Send for approval", "huyDuyet": "Cancel approval", "duyet": "Approve", "tuChoiDuyet": "Refuse to approve", "suaPhieu": "Modify note", "inChiTietPhieuLinhBu": "Print details of duty compensation note", "slKhaDung": "Available quantity", "slTonMoiThucTe": "Actual new inventory quantity", "slTonMoiKhaDung": "Available new inventory quantity", "ngayTaoDon": "Date of invoice creation", "inDanhSachPhatThuoc": "Print medication distribution list", "inDanhSachLinhThuoc": "Print the medication list", "inDanhSachPhatVatTu": "Print List of medical supplies for distribution", "donGiaKeChoNb": "Unit price of prescription for patient", "inChiTietPhieuLinhBuKichCo": "Print details of duty compensation note size", "phatThuoc": {"title": "Medication distribution", "phatThuocNgoaiTruRaVien": "Medication distribution for outpatients and discharge", "danhSachPhatThuocNgoaiTruRaVien": "List of medication distribution for outpatients and discharge", "thanhTien": "Total amount", "bacSiChiDinh": "Indicated by doctor", "giaPhuThu": "Surcharge price", "giaKhongBaoHiem": "Price without insurance", "giaBaoHiem": "Insured price", "trangThaiHoan": "Status of Return", "dvt": "Unit of measurement", "slKe": "Quantity of prescription", "slPhat": "The amount for distribution", "soNgay": "Number of days", "slLan": "Quantity/time", "lanNgay": "Time/day", "hamLuong": "Content", "tenThuoc": "Medication name"}, "thietLapKhoaDichVu": "Setting service lock", "khoaDuocTruyCap": "Department is accessed", "phieuLinhLan": "Obtainment paper", "phieuTraLan": "Return paper", "soLuongLeDaLinhDu": "The odd amount has been obtained in excess", "slThuCapLinhDu": "Secondary Quantity Issued in Surplus", "soLuongLinhTrenKhoTaiKhoa": "The obtained quantity on the warehouse at the department", "loaiThuoc": "Type of medication", "loaiDoiTuong": "Type of Subject", "trangThaiDuyet": "Status of Approval", "timDonThuoc": "Enter Patient Name, patient file code or Scan patient QR Code", "inPhieuNhapKho": "Print Warehouse import note", "trangThaiThuoc": "Medication Status", "tenThuocHamLuong": "Medication name - Content", "slDvtSoCap": "Primary Quantity - Unit of measurement", "slDvtThuCap": "Secondary Quantity - Unit of measurement", "lieuDung": "Dosage", "cachDung": "How to use", "bacSiChiDinh": "Indicated by doctor", "thoiGianChiDinh": "Indication time", "soNgay": "Number of days", "ngayDuyet": "Date of Approval", "phieuTra": "Return note", "trangThaiHoan": "Status of Return", "inPhieuBoSungCoSoTuTruc": "Print note for supplementation of on-duty cabinets", "inChiTietPhieuXuat": "Print Detailed Export note", "inPhieuLinhHaoPhi": "Obtainment paper for Consumption", "chonKhoaNb": "Choose department of the patient", "trangThaiPhieu": "Note Status", "timPhieuLinhLan": "Search obtainment paper", "timSoPhieu": "Search note number", "slGiuCho": "Reserved Quantity", "canThiepDuoc": "Pharmaceutical Intervention", "tuChoiDuyetDLSThanhCong": "Successfully refused approval of clinical pharmacy", "tuChoiDuyetDLSThatBai": "Failed to refuse approval of clinical pharmacy", "nguoiBaoLanh": "Guarant<PERSON>", "phongKhamChiDinh": "Indicating clinic", "chanDoan": "Diagnosis", "ghiChuKhoaDuoc": "Pharmacy Department Note", "thanhTienSauVat": "Total Amount after VAT", "sLThuCapYeuCau": "Requested Secondary Quantity", "sLsoCapYeuCau": "Requested Primary Quantity", "sLSoCapDuyet": "Approved Primary Quantity", "slThuCapKe": "Prescribed Secondary Quantity", "slSoCapKe": "Prescribed Primary Quantity", "hinhThucXuat": "Export Method", "xemSLTonKhoTaiKhoa": "View Stock Quantity at Department", "tenKhoa": "Department Name", "soLuongTaiKhoa": "Quantity at Department", "soLuongDatTruocTaiKhoa": "Pre-ordered Quantity at Department", "soLuongKhaDungTaiKhoa": "Available Quantity at Department", "phieuXuatTraTuKhoTaiKhoa": "Note of export for return from warehouse at Department", "taoPhieuXuatTraTuKhoTaiKhoa": "Create Note of export for return from warehouse at Department", "khoaXuat": "Export Department", "vuiLongChonKhoaXuat": "Please select export department", "chonKhoaXuat": "Select export department", "chonKhoNhap": "Select import warehouse", "thietLapKhoChiDinh": "Set the indicating department", "quanTriKho": "Inventory management", "quanLyThau": "Bidding management", "vatTuKyGui": "Medical supplies for Consignment", "timKho": "Search inventory", "vuiLongChonKhoaNB": "Please select department of patient", "vuiLongChonNoiTru": "Please select inpatient", "vuiLongChonCapCuu": "Please select emergency", "vuiLongChonCanLamSang": "Please select paraclinical", "vuiLongChonChucVu": "Please select a position", "vuiLongChonTaiKhoan": "Please select an account", "vuiLongNhapMucDoUuTien": "Please enter priority level", "thietLapQuanTriKho": "Set up warehouse management", "maKho": "Warehouse code", "khoaQuanLy": "Department in charge of management", "vuiLongChonKhoaQuanLy": "Please select department of management", "khongSuDung": "Do not use", "coCheDuyetPhat": "Mechanism of Approval/ Distribution", "vuiLongChonCoCheDuyetPhat": "Please select mechanism of Approval/ Distribution", "chonLoaiCoCheDuyetPhat": "Select type of mechanism of Approval/ Distribution", "coCheDuTruLinhBu": "Mechanism of Estimation/Compensation", "chonLoaiCoCheDuTruLinhBu": "Select type of mechanism of Estimation/Compensation", "vuiLongChonLoaiCoCheDuTruLinhBu": "Please select type of mechanism of Estimation/Compensation", "giuChoNgayKhiKe": "Reserve the spot as the prescription is executed", "chonGiuChoNgayKhiKe": "Select to reserve the spot as the listing is executed", "vuiLongChonGiuChoNgayKhiKe": "Please select to reserve the spot as the listing is executed", "tinhChatKho": "Warehouse properties", "chonTinhChatKho": "Select warehouse properties", "vuiLongChonTinhChatKho": "Please select warehouse properties", "thongTinKho": "Warehouse information", "vuiLongNhapMaKho": "Please enter warehouse code", "vuiLongNhapMaKhoKhongQuaNumKyTu": "Please enter warehouse code with no more than {{num}} characters!", "vuiLongNhapTenKho": "Please enter warehouse name", "vuiLongNhapTenDichVuKhongQuaNumKyTu": "Please enter service name with no more than {{num}} characters", "vuiLongChonLoaiKho": "Please select warehouse type", "maCoSoGPP": "GPP establisment code", "vuiLongChonMaCoSoGPP": "Please enter GPP establisment code", "taiKhoanGPP": "GPP account", "vuiLongNhapTaiKhoanGPP": "Please enter GPP account", "matKhauGPP": "GPP password", "vuiLongNhapMatKhauGPP": "Please enter GPP password", "khoTrucThuoc": "Affiliated warehouse", "chonNhanVien": "Select employee", "maNhanVien": "Employee code", "chonHoVaTenNhanVien": "Select first and last name of employee", "nhanVienQuanLy": "Manager", "danhSachDichVuTrongThau": "List of in-bidding services", "danhSachThau": "List of bidding", "chiTietThau": "Bidding detail", "khaiBaoGiamGia": "Declare a discount", "khaiBaoCoSo": "Declare the base", "maDuongDung": "Code of route of administration", "timHamLuong": "Search content", "slTraLai": "Quantity for return", "timSlTraLai": "Search returned quantity", "slCoTheNhap": "Quantity allowed to import", "timSlCoTheNhap": "Search quantity allowed to import", "tatCaQuyetDinhThau": "All Bidding Decisions", "chonLoaiPhieu": "Select note type", "chonNguonNhapKho": "Select import source of warehouse", "nhapSoHoaDon": "Enter invoice number", "chonThangDuTru": "Select estimated month", "vuiLongChonThangDuTru": "Please select estimated month", "timKiemPhieuTraLan": "Search return note", "timKiemSoPhieu": "Search note number", "luuVaGuiDuyet": "Save and send for approval", "giaTruocVAT": "Price before VAT", "hsd": "Expiry date", "chiTietPhieuNhapKhac": "Detail of other input note", "danhSachHangHoaTra": "List of returned goods", "danhSachNguoiBenhTrongPhieuTra": "List of patients in the return note", "nhapMaHieu": "Enter code", "nhapSoLo": "Enter lot number", "nhapGiaTruocVAT": "Enter price before VAT", "nhapGiaSauVAT": "Enter price after VAT", "nhapVAT": "Enter VAT", "chonXuatXu": "Choose origin", "slNhapVaoLonHonSLConLaiCuaThau": "The imported quantity is greater than the remaining bidding quantity", "thongTinPhieuNhapKhac": "Other input information", "loaiNhapXuat": "Type of Import/export", "vuiLongChonLoaiNhapXuat": "Please select type of Import/export", "chiTietPhieuNhapChuyenKho": "Detail of note of warehouse import & transfer", "thanhTienTheoGiaNhap": "Total amount (by buying price)", "thanhTienTheoGiaBan": "Total amount (by selling price)", "phieuLinhBu": "Compensation note", "phieuLinhNoiTru": "Inpatient conpensation note", "timKiemTheoTenHangHoa": "Search by name of goods", "thongTinPhieuLinhBuTuTruc": "Information of duty compensation note", "thoiGianPhatHangHoa": "Delivery time of goods", "chonLoaiPhieuNhap": "Select type of input note", "xemPhieuDuyetDuTru": "View the approval note of estimation", "xemPhieuXuat": "View export note", "xemPhieuLinh": "View obtainment paper", "xuatVacXinTiem": "Export vaccines", "khoaTraVeKho": "Department return to warehouse", "chiTietPhieuXuatChuyenKho": "Detail of note of warehouse export & transfer", "chiTietPhieuXuatTraNhaCungCap": "Detail of note of warehouse export & return to the supplier", "chiTietPhieuXuatKhac": "Detail of other notes of export", "chiTietPhieuXuatVacXinTiem": "Detail of note of vaccine export", "chiTietPhieuXuatTraKhoTaiKhoa": "Detail of note of export & return to the warehouse at department", "phieuXuatTraKhoTaiKhoa": "Note of export & return to the warehouse at department", "xuatSlTaiSuDung": "Export quantity for reuse", "khoDoiUng": "Counterpart warehouse", "khoaLinh": "Receiving department", "linhChoKho": "Receiving for warehouse", "danhSachHangHoaLinh": "List of goods for receiving", "soLuongLeLinhChoLanSau": "Odd quantity received for next time", "soLuongLeLinhChoLanTruoc": "Odd quantity received for the previous time", "slKeChuaLenPhieuLinh": "Listed quantity not displaying in obtainment paper", "slSoCapLeLinhBuChoLanTruoc": "Odd primary quantity compensating for the previous time", "slSoCapKeChuaLenPhieuLinh": "Listed primary quantity not displaying in the obtainment paper", "nguoiBaoLanhSdt": "Guarantor - Phone number", "diaChiNhaCungCap": "Address of supplier", "chiTietTonKho": "Inventory details", "huyGiuTon": "Cancel the hold of inventory", "giuTon": "Hold the inventory", "luuThietLap": "Save settings", "thietLapKhoaDuocTruyCap": "Set up the department to be accessed", "lichSuNhapXuat": "History of import and export", "dsNhap": "Import list", "dsXuat": "Export list", "dsGiuCho": "Reservation list", "dsNbHuyGiuTon": "List of patients cancelling the hold of inventory", "nhapHoTenNb": "Enter full name of patient", "soLuongXuat": "Output quantity", "chonTrangThaiPhieu": "Select status of note", "chonNgayPhat": "Select date of distribution", "phongKhamSangLoc": "Screening clinic", "xemDonThuoc": "See prescription", "dsChiTiet": "Detailed list", "lyDoHuyPhat": "Reason for cancellation of distribution", "dienLyDoHuyPhat": "Fill in reason for cancellation of distribution", "huyPhat": "Cancel distribution", "choDuyetDLS": "Waiting for approval of clinical pharmacy", "daDuyetDLS": "Approved clinical pharmacy", "tuChoiDuyetDLS": "Refused to approve clinical pharmacy", "donThuocNhaThuoc": "Pharmacy prescription", "thuocLinhNoiTru": "Inpatient medicine", "thuocRaVien": "Discharge medicine", "phieuNhapHoanTraTuTruc": "Return note of medicine returned to the duty medicine cabinet", "phieuXuatTraLaiKho": "Export note for returning to warehouse", "trangThaiDuyetDls": "Status of clinical pharmacy", "huyCanThiepDuoc": "Cancel pharmaceutical intervention", "huyCanThiepDuocConforMsg": "Are you sure you want to cancel the pharmaceutical intervention?", "capNhatGiaHSD": "Update price/expiry date", "capNhatGia": "Update price", "capNhatHSD": "Update expiry date", "guiDuyetThanhCong": "Submitted successfully for approval", "updateSuccess": "Updated data successfully", "themMoiPhieuNhap": "Add new input note", "nhapKhongTheoThau": "Import without bidding", "chuaTaoHoaDon": "Invoice has not been created yet", "goiThauDaHetHopDongNgay": "The contract of the bidding package has expired", "goiThauDaHetHieuLucNgay": "The contract of the bidding package is invalid on", "khoTaiKhoa": "Warehouse at the department", "nhapThanhTien": "Enter total amount", "tenHangHoaLaBatBuoc": "Name of goods is required!", "tenKhoLaBatBuoc": "Warehouse name is required!", "giaNhapTruocVATLaBatBuoc": "Import price before VAT is required!", "nhapGiaNhapTruocVAT": "Enter import price before VAT", "nhapGiaNhapSauVAT": "Enter import price after VAT", "nhapGiaNhapSauVATLaBatBuoc": "Import price after VAT is required!", "donGiaKhongBHLaBatBuoc": "Uninsured unit price is required!", "donGiaBHLaBatBuoc": "Insured unit price is required!", "phuThuLaBatBuoc": "Surcharge is required!", "capNhatGiaChoNB": "Update price for patient", "capNhatHanSuDung": "Update expiry date", "hsdHienTai": "Current expiry date", "hsdMoi": "New expiry date", "hsdMoiLaBatBuoc": "New expiry date is required", "chonHsdMoi": "Select new expiry date", "giaTriChiTuNum": "Value ​​from {{ num }} only!", "deXuatDuTruHangHoaVaoKho": "Propose estimation of goods to the warehouse", "phieuNhapTuNhaCungCap": "Import note from supplier", "nhapHangHoaTuNhaCungCapVaoKho": "Importing goods from suppliers into warehouse", "taoPhieuLinhBuBoSungTuTruc": "Create compensation note supplementing for duty medicine cabinet", "phieuNhapKhac": "Other import notes", "taoPhieuNhapDuocTangDuocTaiTro": "Create import notes that are donated, sponsored,...", "csytChuyenToi": "The health care facility moving in", "vuiLongChonCsytChuyenToi": "Please select health care facility moving in", "dvtThucap": "Unit of Measurement (secondary)", "dvtSocap": "Unit of Measurement (primary)", "slTraThuCap": "Returned quantity (secondary)", "slTraSoCap": "Returned quantity (primary)", "ngayThucHien": "Execution date", "taoMoiGiuCho": "Create new and reserve spot", "ngungSuDung": "Stop using", "daScan": "Scanned", "chuaScan": "Not scanned yet", "donThuocChuaDuocDuyetLamSang": "The prescription has not been clinically approved", "tiepTucPhatThuoc": "Continue distributing medications", "dsHangHoaVuotCoSo": "The inventory of goods exceeds the base number", "slVuotCoSO": "Secondary Quantity Issued in Surplus", "coSoHienTai": "Current base", "xuatChuyenTraKho": "Export and return to warehouse", "vuiLongChonLoaiXuat": "Please select type of export", "capNhatCoSoTuTruc": "Update the number of on-duty cabinets", "chonTenHangHoa": "Select the product name", "vuiLongChonTenHangHoa": "Please select the product name", "vuiLongChonKho": "Please select the warehouse", "coSoTuTrucHienTai": "Current number of on-duty cabinets", "coSoTuTrucMoi": "New number of on-duty cabinets", "vuiLongNhapCoSoTuTrucMoi": "Please enter the new on-call cabinet number", "capNhatCoSoTuTrucThanhCong": "Updating the live cabinet number successfully!", "xayRaLoiVuiLongThuLai": "An error occurred, please try again!", "nhapCoSoTuTrucMoi": "Enter the new on-call cabinet number", "slHienTai": "Current quantity", "slSoCapLinhDu": "Primary quantity remaining", "diaDiemKe": "Statistics location", "trangThaiDonThuoc": "Prescription status", "thoiGianKeDon": "Prescription time", "khoNhan": "Receiving warehouse", "capNhatSoLo": "Update lot number", "soLoHienTai": "Current lot number", "soLoMoi": "New lot number", "soLoMoiLaBatBuoc": "New lot number is required", "nhapSoLoMoi": "Enter the new lot number", "suaSoLuongDuyetThanhCong": "Fix the number of successful approvals", "tuTruc": "On duty cabinet", "chonLoai": "Select type", "vuiLongChonLoai": "Please select type", "chonTenKho": "Select the warehouse name", "vuiLongChonTenKho": "Please select a warehouse name", "thoiGianHoaDon": "Invoice time", "trongThau": "In bidding", "ngoaiThau": "Outsourcing", "doiTuongDuocPhepKe": "Subjects allowed to be prescribed", "danhSachNBTrongPhieuLinh": "List of beneficiaries in the receipt", "phieuXuatKhac": "Other delivery notes", "thietLapDoiTuongKe": "Set up the inventory object", "tiepTucThanhToan": "Continue payment", "dieuChinhCoSoTren": "Adjust the base on", "capNhatCoSoTren": "Update base on", "coSoTrenSauDuyet": "Base after review", "capNhatCoSoDuoi": "Update base below", "dieuChinhCoSoDuoi": "Adjust the base below", "coSoDuoiSauDuyet": "Base after review", "capNhatCoSoDuoiThanhCong": "Base update successful!", "chonLoaiDonThuoc": "Select prescription type", "nhapTrangThaiThuoc": "Enter drug status", "nhapTenThuocHamLuong": "Enter drug name - strength", "nhapNguoiDuyet": "Enter reviewer", "nhapTrangThaiHoan": "Enter completion status", "timMaHangHoa": "Find product code", "timTenHangHoa": "Find product name", "timTenHoatChat": "Find active ingredient name", "nhaSanXuat": "Manufacturer", "xoaDuLieu": "Delete data", "banCoChacChanMuonHuyDuyetKhong": "Are you sure you want to cancel approval?", "duyetDuocLamSangThanhCong": "Clinical review successful", "duyetDuocLamSangThatBai": "Clinical review failed", "huyDuyetDuocLamSangThanhCong": "Clinical approval cancelled successfully", "timSoLuongKhaDung": "Find available quantity", "timDonViTinh": "Find the unit of measure", "timSoLo": "Find lot number", "nguoiDuyetPhieuLinh": "The person who approves the receipt", "chonTrangThaiThuoc": "Select drug status", "vatBan": "VAT sales", "themMoiVaGuiDuyetThanhCong": "Add new and submit for approval successfully", "themVaoKhoThanhCong": "Add to stock successfully", "capNhatThanhCong": "Update successful", "xoaPhieuThanhCong": "Voucher deleted successfully!", "huyGuiDuyetThanhCong": "Cancel submission successful", "huyDuyetThanhCong": "Cancel approval successful", "duyetThanhCong": "Browse successfully", "xoaHangHoaThanhCong": "Deleted goods successfully!", "taiKhoanDuocTruyCap": "Account accessed", "thietLapTaiKhoanDuocTruyCap": "Set up account access", "xacNhanXoaNhanVien": "Confirm deletion of employee {{name}}?", "xoaPhieuLinhBu": "Delete compensation voucher", "chiTietPhieuXuat": "Delivery note details", "khongCo": "Do not have", "timMaHs": "Find file code", "nguoiDuyetPhieu": "Vote reviewer", "khoTuTruc": "Direct warehouse", "loaiDv": "Service type", "xacNhanXoaPhieu": "Confirm deletion of ticket", "xemLichSuSuDungThuocKhangSinh": "View antibiotic use history", "lichSuSuDungThuocKhangSinh": "History of antibiotic use", "nhapKhac": {"themMoiPhieu": "Add new import voucher", "suaPhieu": "Edit other import invoices"}, "chonKhoTuTruc": "Select direct warehouse", "vuiLongChonKhoTuTruc": "Please select direct warehouse", "vuiLongChonTuNgay": "Please select from date", "vuiLongChonDenNgay": "Please select date", "taoMoiVaGuiDuyetPhieuThanhCong": "Create new and send approval ticket successfully", "chonCacDieuKienDeTaoPhieuLinhBu": "Select the conditions to create a compensation voucher", "taoPhieu": "Create ticket", "taoVaGuiDuyet": "Create and submit for approval", "nhapUuTien": "Enter priority", "xuatTheoLo": "Batch export", "soLuongThau": "Number of bids", "goiThau": "Bidding Package", "duongDung": "Route of administration", "quyCach": "Specifications", "xuatXu": "Origin", "maHoatChat": "Active ingredient code", "soLuongKhaDung": "Available Quantity", "soLuongConLai": "Remaining quantity", "taoHoaDon": "Create invoice", "taoHoaDonVtytKyGui": "Create invoice for medical supplies consigned", "ngayTaoHoaDon": "Invoice creation date", "khoKyGui": "Consignment warehouse", "vuiLongNhapThoiGianThucHien": "Please enter execution time", "vuiLongNhapKhoKyGui": "Please enter the consignment warehouse", "vuiLongNhapNhaCungCap": "Please enter supplier", "vuiLongNhapKho": "Please enter warehouse", "vuiLongNhapNguonNhapKho": "Please enter the source of the import", "vuiLongNhapHinhThuc": "Please enter form", "vuiLongNhapNgayHoaDon": "Please enter invoice date", "taoHoaDonKyGuiThanhCong": "Created deposit invoice successfully", "nhapTuNhaCungCap": "Import from supplier", "duTru": "Forecast", "chuyenKho": "Transfer warehouse", "taiKhoanKhoTaiChinhKeToan": "Financial accounting warehouse account", "vuiLongNhapTaiKhoanKhoTaiChinhKeToan": "Please enter your accounting warehouse account", "dsPhieuTuVanThuoc": "List of drug consultation forms", "chiTietPhieuTuVanThuoc": "Drug consultation form details", "tuVanThuoc": {"tuVanThuoc": "Drug consultation", "canThiepLan": "Intervention times", "ngayCanThiep": "Intervention date", "taiKhoanTuVan": "Consulting account", "mucDoDongY": "Level of agreement", "yKien": "Opinion", "ngayXacNhan": "Confirmation date", "taiKhoanXacNhan": "Confirmed account", "noiDungDeNghiTuVan": "Content of consultation request", "nguoiTaoPhieu": "Voting creator", "nguoiTuVan": "Consultant", "yKienSauTuVan": "Comments after consultation", "nguoiXacNhan": "The confirmer", "taoPhieuTuVanThuoc": "Create a drug consultation form", "deNghiTuVan": "Request for advice", "trangThaiTuVanThuoc": "Drug consultation status", "tenHoatChat": "Active ingredient name", "tenHoatChatKhac": "Other active ingredient names", "nhomThuoc": "Drug group", "taiLieuThamKhao": "References", "maVanDeChung": "General problem code", "maVanDeCuThe": "Specific problem code", "maCanThiepChung": "General intervention code", "maCanThiepCuThe": "Specific intervention code", "tuVanCuThe": "Specific advice", "taiLieuThamKhaoKhac": "Other references", "chiTietThuocTuVan": "Drug consultation details", "maPhieu": "Coupon code", "noiDungDeNghiTuVanThuoc": "Content of drug consultation request", "lyDo": "Reason", "gui": "Send", "thongTinTuVan": "Consulting information", "xacNhanTuVan": "Confirm consultation", "danhSachThuocDongYTheoTuVan": "List of drugs agreed upon by consultation", "guiTuVanThuocThanhCong": "Send drug consultation successfully", "huyGuiTuVanThuocThanhCong": "Cancel sending drug consultation successfully", "xacNhanTuVanThuocThanhCong": "Confirm successful drug consultation", "huyXacNhanTuVanThuocThanhCong": "Cancel successful drug consultation confirmation", "huyGui": "Cancel send", "huyXacNhan": "Cancel confirmation", "themThuocTuVan": "Add medication advice", "themMoiThuocTuVanThanhCong": "New drug consultation successful", "capNhatThuocTuVanThanhCong": "Update drug consultation successfully", "xoaThuocTuVanThanhCong": "Delete medication advice successfully", "hoatChatDaDuocTuVan": "Active ingredient has been advised", "chiDinhSangToDieuTri": "Assign to Treatment Sheet", "nbKhongCoToDieuTriThoaManDieuKien": "patient  does not have a treatment sheet that satisfies the conditions", "vuiLongChonMucDoDongY": "Please select the level of agreement."}, "thuocNgoaiTru": "Outpatient medicine", "thuocNhaThuoc": "Pharmacy drugs", "thuocMuaNgoai": "Over-the-counter drugs", "thuocKhoBHYT": "Health insurance warehouse medicine", "tienChietKhau": "Discount", "chietKhau": "Discount", "nhapChietKhau": "Enter discount", "phanTramChietKhau": "Discount percentage", "giaTruocVATChuaChietKhau": "Price before VAT, no discount", "kyHieuHDDT": "HDDT symbol", "maKhoTCKT": "TCKT warehouse code", "nhapMaKhoTCKT": "Enter TCKT warehouse code", "tenKhoTCKT": "TCKT warehouse name", "nhapTenKhoTCKT": "Enter TCKT warehouse name", "loaiPhieuTra": "Type of payment voucher", "dotXuat": "Suddenly", "nhapGiaTruocVATChuaChietKhau": "Enter price before VAT without discount", "apDungGoi": "Apply package", "ptTt": "Surgery and procedure", "chiTietNbLinh": "Details patient  received", "lichSuKeThuoc": "Prescription history", "kiemTraTuongTacThuoc": "Check drug interactions", "nguoiBenhKhongCoTuongTacThuoc": "Patients have no drug interactions", "phieuXuatDaoHanSuDung": "Expiry date reversal voucher", "taoPhieuXuatDaoHanSuDung": "Create a reverse expiry date voucher", "chiTietPhieuXuatDaoHanSuDung": "Expiry date reversal voucher details", "xemPhieuDaoHanSuDung": "View expiry date coupon", "daoHanSuDung": "Expiry date island", "coSoDuoi": "Base below", "nhapCoSoDuoi": "Enter the base below", "vuiLongNhapCoSoDuoi": "Please enter the base below", "coSoTren": "Base on", "tyLeBhTt": "Insurance Rate", "lichSuKe": "History of the chart", "tenPtTt": "Name of surgical procedure", "thuocLinhDuTru": "Prescription drugs", "noiDungCanhBaoCoSoDuoi": "<span>0 &#8804; lower base &#8804; upper base</span>", "trangThaiDls": "DLS Status", "xemThuocBenhNhanSuDung": "View patient medications", "coSoTrenHienTai": "Current base", "coSoDuoiHienTai": "Current base", "capNhapCoSoTren": "Update base on", "coSoTrenMoi": "New base", "kyHieuSoPhieuNhap": "Voucher number symbol", "lichSu": {"lichSuSuDungThuoc": "History of medication use", "thgChiDinh": "Designation", "thgPhat": "The development"}, "danhSachHangHoaChoTra": "List of goods awaiting return", "danhSachHangHoaChoLinh": "List of goods awaiting receipt", "soLuongDuyet": "Number of approvals", "nhomChiDinh": "Designated group", "phongDuocTruyCap": "Room Accessed", "thietLapPhongDuocTruyCap": "Set up rooms to be accessed", "coSoDuoiCapNhat": "Base under update", "loaiCapNhat": "Update type", "vuiLongChonLoaiCapNhat": "Please select update type!", "chonLoaiCapNhat": "Select update type", "capNhatMotHangHoaONhieuKho": "Update 1 item in multiple warehouses", "capNhatNhieuHangHoaOMotKho": "Update multiple items in 1 warehouse", "slVuotCoSoTren": "Quantity Exceeds Upper Limit", "thietLapChonKho": "Set up warehouse selection", "cdhaThuThuat": "CDHA, Procedure", "vtyt": "Medical Equipment", "pttt": "Surgery and procedure", "nhomKhac": "Other Groups", "danhSachSuatAnChuaLinhDuyetTra": "List of meals not received, approved for payment", "chuaTra": "Not paid yet", "inTemThuocNCC": "Print drug label", "thuocCanThiep": "Interventional medicine", "vanDeCanThiep": "Intervention issues", "nguoiBenhCanThiep": "Patient intervention", "tuVanThuocNb": "patient  drug consultation", "tuVanThuocChoNguoiBenh": "Drug consultation for patients", "hinhThucTuVan": "Consulting form", "noiDungTuVan": "Consulting content", "doiTuongTuVan": "Consulting object", "thuocTuVan": "Drug advice", "tuVanCachDung": "Advice on usage", "duocSiTuVan": "Pharmacist consultant", "cachDungTuVan": "How to use advice", "ngayTuVan": "Consulting Day", "boSung": "Additional", "chonBoSung": "Select additional", "chonDotXuat": "Select suddenly", "daThanhToan": "Paid (indications from medical examination)", "hangHoaTuKhamBenhChuaThanhToanKhongDuocLenPhieuLinh": "Unpaid medical bills cannot be refunded!", "vuiLongChonItNhatMotPhieuNhap": "Please select at least one entry ticket", "tyLeBhCuaNb": "patient 's insurance rate %", "tyLeThanhToanDv": "Service payment rate", "tyLeBhCuaDv": "Insurance % of Service", "tienNbCungChiTra": "patient  co-pay", "danhSachPhieuXuat": "List of export vouchers", "thoiGianTao": "Creation time", "phieuThucHienCongKhaiThuocNbTheoPhieuLinh": "Public declaration of patient  medicine (according to receipt form)", "capNhatTonTuKhoPharmacity": "Update inventory from Pharmacity warehouse", "xuat": {"duyet": "Approve", "huyDuyet": "Cancel approval", "tuChoiDuyet": "Refuse to approve", "trangThai": "Status"}, "trangThaiChoDuyet": "Waiting for approval", "trangThaiHoanThanh": "Completed", "tyLeTtDv": "Service transaction rate", "capNhatTonKhoThanhCong": "Inventory updated successfully.", "inBienBanKiemNhap": "In the inspection report", "vuiLongChonHoiDongKiemKe": "Please select the inventory committee!", "hienThiTungKho": "Display each warehouse", "xuatDULieuDanhSachTonKho": "Export inventory list data", "chonHoiDongKiemNhap": "Select the inspection committee", "ngayHoanThanh": "Completion date", "phieuLinhThuocDieuTriNgoaiTru": "Outpatient medication receipt", "danhSachChoXnNhap": "Waiting list for test input", "choXnNhap": "Waiting for test input", "xacNhanNhap": "Confirm entry", "nhapTheoLo": "Batch input", "nhapHsdXaNhat": "Enter the farthest expiration date", "huyDuyetNhap": "Cancel import approval", "choXacNhanNhap": "Waiting for import confirmation", "xuatFileChiTietPhieuXuat": "Imaging Diagnosis", "xuatFileChiTietPhieu": "Export detailed invoice file", "xuatFilePhieuLinhHaoPhi": "Export Expense Receipt File", "xuatFilePhieuXuatTraLaiKho": "Export Return Slip to Warehouse", "inTemTuVanThuoc": "In the drug consultation room", "xuatFileDanhSachBenhNhan": "Export Patient List", "xuatFilePhieuBoSungCoSoTuTruc": "Export the Supplementary Inventory Report for the Duty Cabinet", "xuatFilePhieuNhapKho": "Export Warehouse Receipt", "xuatFilePhieuNhapHoanTraTuTruc": "Export Return Receipt File for Direct Cabinet", "xuatFilePhieuTra": "Export Return Slip", "inPhieuNhapTra": "In the return receipt", "xuatFileChiTietPhieuNhap": "Export Detailed Import Receipt", "xuatFilePhieuNhapTra": "Export Return Receipt File", "xuatFileChiTietPhieuLinhBu": "Export detailed reimbursement receipt file.", "yKienBacSi": "Doctor's opinion", "noiDungCanhBaoTuongTac": "Interactive warning content", "loaiCanhBao": "Warning type", "inBienBanGiaoNhanVacxin": "In the vaccine delivery receipt", "tenNhaSanXuat": "Manufacturer name", "lapBieu": "Create a form", "hopDongDaHetHieuLucDate": "The contract has expired on: {{ngayHetHanHopDong}}", "slTraLaiNcc": "Quantity returned to supplier", "danhSachBanGiaoThuocVatTu": "List of drug and material handover", "ngayBanGiao": "Delivery date", "khoaBanGiao": "Department handover", "nguoiBanGiao": "Deliverer", "chiTietBanGiaoThuocVatTu": "Details of drug and supplies handover", "thongTinBanGiao": "Handover information", "nguoiGiao": "Delivery person", "ngayNhan": "Date received", "nguoiNhan": "Recipient", "lyDoThuaThieu": "Reason for surplus/deficit", "vuiLongChonkhoaBanGiao": "Please select the transfer department.", "vuiLongThoiGianBanGiao": "Please select the handover time.", "chiTietThuocVatTuBanGiao": "Details of medicine/supplies handed over", "soLuongNhan": "Quantity received", "soLuongBoSung": "Supplementary quantity", "soLuongSuDung": "Usage quantity", "soLuongBanGiao": "Delivery quantity", "banCoChacChanMuonXoaBanGhi": "Are you sure you want to delete the record?", "thanhToanNcc": "Payment to supplier", "huyThanhToanNcc": "Cancel supplier payment", "huyThanhToanThanhCong": "Payment cancellation successful!", "daThanhToanNcc": "Payment has been made to the supplier.", "taiKhoanThanhToan": "Payment account", "themMoiBanGiaoThuocVatTu": "Add new drug and supplies delivery.", "themMoiThuocVTBanGiao": "Add medicine, Supplies handover", "tenThuocVT": "Drug name, Supplies", "chonTenThuocVT": "Select Drug Name, Supplies", "nhapSoLuongNhan": "Enter the quantity received", "nhapSoLuongBoSung": "Enter the additional quantity", "nhapSoLuongSuDung": "Enter the quantity used", "nhapSoLuongBanGiao": "Enter the quantity of delivery", "nhapSoLuongTraLai": "Enter the quantity to return", "vuiLongChonTenThuocVT": "Please select the name of the medicine, Supp<PERSON>.", "xacNhanBanGiao": "Confirmation of handover", "huyXacNhanBanGiao": "Cancel handover confirmation", "xacNhanbanGiaoThanhCong": "Successful handover confirmation!", "huyXacNhanBanGiaoThanhCong": "Cancellation of successful handover confirmed!", "boChonTatCa": "Deselect all", "chinhSuaThuocVatTuBanGiao": "Edit medication, handover supplies", "chinhSuaThuocVTBanGiao": "Edit medication, Supplies handover", "thuocTraNoiTru": "Inpatient medication", "danhSachBanGiaoThuocDungCuThuongTruc": "List of handover medications and permanent equipment", "chiTietBanGiaoThuocDungCuThuongTruc": "Details of drug and equipment handover", "coSoTrenKhoNhap": "Base on inventory input", "coSoDuoiKhoNhap": "Base stock under inventory input", "nhapTenHangHoaF2": "Enter the item name [F2]", "boBatBuocSoLo": "Remove mandatory lot number", "boBatBuocHSD": "Remove mandatory expiration date", "slDuyetSoCap": "Primary SL review", "slYeuCauSoCap": "SL requires primary care.", "inBienBanThanhLy": "In the settlement report", "soLuongTonThucTeSoCap": "Actual inventory quantity (primary)", "khoDuocDuTru": "Expected expenses", "thietLapKhoDuocDuTru": "Planned inventory setup", "phieuTraNoiTru": "Inpatient discharge slip", "loaiTon": "Type of inventory", "vuiLongNhapSoLuongBanGiao": "Please enter the quantity of deliveries!", "nhapDuLieuTtKho": "Input inventory data", "xuatDuLieuTtKho": "Export inventory data", "nhapDuLieuKhoTrucThuoc": "Enter data for affiliated inventory.", "xuatDuLieuKhoTrucThuoc": "Export inventory data from the department.", "nhapDuLieuNhanVienQuanLy": "Data entry Management staff", "xuatDuLieuNhanVienQuanLy": "Export employee management data", "suaThoiGianDuyetPhieu": "Adjust approval time for the claim.", "sttXuatThuoc": "Medication Dispensing Statement", "xuatSTT": "Output serial number", "daXuat": "Issued", "tuSo": "From number", "denSo": "To the number", "tuSttXuatThuoc": "From the prescription order", "denSttXuatThuoc": "To the prescription number", "nhapTuSoVaDenSo": "Please enter from number and to number.", "xuatSttKhongThanhCong": "Failed to issue invoice number.", "xuatSttThanhCong": "Successfully issued the serial number.", "huyXuat": "Cancel invoice", "lyDoHuyXuat": "Reason for cancellation", "dienLyDoHuyXuat": "Enter the reason for cancellation.", "huyXuatThanhCong": "Cancellation successful", "huyXuatKhongThanhCong": "Cancellation unsuccessful", "slTonThucTeSoCapDdls": "Primary care SL exists.", "slTonKhaDungSoCapDdls": "Primary available SL stock", "theoThoiGianChiDinh": "According to the specified time", "phieuXuatHuy": "Cancellation voucher"}