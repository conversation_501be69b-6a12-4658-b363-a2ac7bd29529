{"thietLapThongSo": "THIẾT LẬP THÔNG SỐ", "tenTruongTrenMhKiosk": "Tên trư<PERSON><PERSON> trên <PERSON>k", "hienThi": "<PERSON><PERSON><PERSON> thị", "tenThietBi": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> bị", "bacSi": "<PERSON><PERSON><PERSON>", "yTa": "Y tá", "hoTro": "Hỗ trợ", "diaChiMac": "Địa chỉ MAC", "quayTiepDon": "<PERSON><PERSON><PERSON><PERSON> ti<PERSON><PERSON> đ<PERSON>", "quayThuNgan": "<PERSON><PERSON><PERSON><PERSON> thu ngân", "thoiGianLamViec": "<PERSON><PERSON><PERSON><PERSON> gian làm vi<PERSON>c", "mauQms": "Mẫu QMS", "datLai": "Đặt lại", "phongQuay": "Phòng/Quầy", "DANG_TIEP_DON": "ĐANG TIẾP ĐÓN", "DANG_KHAM": "ĐANG KHÁM", "CHO_TIEP_DON": "CHỜ TIẾP ĐÓN", "CHO_KHAM": "CHỜ KHÁM", "heThongXepHangCho": "HỆ THỐNG XẾP HÀNG CHỜ", "xinKinhChaoQuyKhach": "<PERSON><PERSON> k<PERSON>h ch<PERSON>", "thietLap": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "chonLoaiQms": "<PERSON><PERSON><PERSON> Q<PERSON>", "chonMotThietLapThongSoCoSanDuoiDay": "<PERSON><PERSON><PERSON> một thiết lập thông số có sẵn dưới đây hoặc", "goiNho": "GỌI NHỠ", "goiNhoEng": "MISSED CALL", "dSNBCoKQCLS": "<PERSON><PERSON> s<PERSON>ch NB có kết quả CLS", "dSNBCoKQCLSEng": "List of patients with paraclinical results", "dangTiepDon": "ĐANG TIẾP ĐÓN", "dangTiepDonEng": "RECEPTION IN PROGRESS", "dangKham": "ĐANG KHÁM", "dangKhamEng": "IN CONSULTATION", "choTiepDon": "CHỜ TIẾP ĐÓN", "choTiepDonEng": "PENDING RECEPTION", "choKetLuan": "CHỜ KẾT LUẬN", "choKetLuanEng": "PENDING DIAGNOSIS", "choThucHien": "CHỜ THỰC HIỆN", "choThucHienEng": "PENDING PROCEDURE", "choKham": "CHỜ KHÁM", "choKhamEng": "PENDING EXAMINATION", "khongTheCheckinKhiThietLap": "<PERSON><PERSON><PERSON><PERSON> thể checkin khi thiết lập hiển thị nhiều phòng", "checkInDuLieuSuccess": "CheckIn dữ liệu thành công!", "khongLamViec": "<PERSON><PERSON><PERSON><PERSON> làm vi<PERSON>c", "dangLamViec": "<PERSON><PERSON> làm vi<PERSON>c", "troLyYta": "<PERSON><PERSON><PERSON> lý - Y tá", "hoTroHuongDan": "Hỗ trợ - Hướng dẫn", "dangKhamBr": "ĐANG <br /> KHÁM", "tiepTheoBr": "TIẾP <br /> THEO", "dangThucHienBr": "ĐANG <br /> THỰC HIỆN", "qrCodeDescription": "Hãy đưa phiếu chứa QR code vào vùng quét dưới đây để hệ thống xác nhận.", "quetMaQr": "Quét mã QR code", "daXacNhan": "ĐÃ XÁC NHẬN", "choXacNhan": "CHỜ XÁC NHẬN", "benhNhanGoiNho": "BỆNH NHÂN GỌI NHỠ", "vuiLongChonThoiGianLamViec": "Vui lòng chọn thời gian làm việc!", "vuiLongNhapTenThietBi": "<PERSON>ui lòng nhập tên thiết bị!", "nhapTenThietBi": "<PERSON><PERSON><PERSON><PERSON> tên thiết bị", "vuiLongChonKhoa": "Vui lòng chọn khoa!", "chonKhoa": "<PERSON><PERSON><PERSON>", "vuiLongChonQuayTiepDon": "<PERSON>ui lòng chọn quầy tiếp đón!", "vuiLongChonQuayThuNgan": "Vui lòng chọn quầy thu ngân!", "chonQuayTiepDon": "<PERSON><PERSON><PERSON> quầy tiếp đón!", "chonQuayThuNgan": "<PERSON><PERSON>n quầy thu ngân!", "vuiLongChonMaMau": "<PERSON>ui lòng chọn mã mẫu!", "vuiLongChonTemplate": "<PERSON><PERSON> lòng chọn <PERSON>late", "chonTemplate": "<PERSON><PERSON><PERSON>", "luaChonPhongKhamVaBacSi": "Lựa chọn phòng khám và bác sĩ", "nhapDiaChiMac": "<PERSON><PERSON><PERSON><PERSON> địa chỉ Mac", "vuiLongChonYta": "Vui lòng chọn trợ lý y tá!", "chonTroLyYta": "<PERSON><PERSON>n trợ lý y tá", "vuiLongChonHoTroHd": "<PERSON>ui lòng chọn hỗ trợ HD!", "chonHoTroHd": "<PERSON>ọn hỗ trợ HD", "sangTu": "<PERSON><PERSON><PERSON> từ", "den": "<PERSON><PERSON><PERSON>", "chieuTu": "<PERSON><PERSON><PERSON> từ", "loaiQms": "Loại QMS", "tongTienGiam": "<PERSON><PERSON><PERSON> g<PERSON>", "tongTienDichVu": "<PERSON><PERSON><PERSON><PERSON> g<PERSON><PERSON><PERSON> d<PERSON> v<PERSON>", "%giamDichVu": "% gi<PERSON><PERSON> d<PERSON>ch vụ", "tenTruongTrenMh": "Tên trườ<PERSON> trên <PERSON>", "diaChiMacThietBi": "Địa chỉ Mac thiết bị", "nhapDiaChiMacThietBi": "<PERSON><PERSON><PERSON><PERSON> địa chỉ mac thiết bị", "vuiLongChonLoaiQms": "Vui lòng chọn loại qms!", "videoGioiThieu": "Video giới thiệu", "chonHoTroHuongDan": "<PERSON>ọn hỗ trợ hướng dẫn", "loaiDanhSach": "<PERSON><PERSON><PERSON> danh s<PERSON>ch", "vuiLongChonLoaiDanhSach": "<PERSON><PERSON> lòng chọn lo<PERSON>i danh s<PERSON>ch", "chonLoaiDanhSach": "<PERSON><PERSON><PERSON> lo<PERSON>i danh s<PERSON>ch", "choPhauThuat": "Ch<PERSON> phẫu thuật", "choPhauThuatEng": "PENDING SURGERY", "choPt": "ChỜ PT", "dangPhauThuat": "<PERSON><PERSON> phẫu thuật", "dangPhauThuatEng": "IN PROGRESS SURGERY", "dangPt": "Đang PT", "xongPhauThuat": "<PERSON><PERSON> phẫu thuật", "xongPhauThuatEng": "COMPLETED SURGERY", "xongPt": "Xong PT", "vuiLongChonKhuVuc": "<PERSON><PERSON> lòng chọn khu vực", "daChuyenPhongHoiTinh": "<PERSON><PERSON> chuyển phòng hồi tỉnh", "batDau": "<PERSON><PERSON><PERSON> đ<PERSON>u", "danhSachLichPhauThuat": "<PERSON><PERSON> s<PERSON>ch lịch phẫu thuật", "danhSachLichPhauThuatEng": "LIST OF SURGERY SCHEDULE", "khoaTitle": "<PERSON><PERSON><PERSON> {{title}}", "khuVucTitle": "<PERSON><PERSON> v<PERSON> {{title}}", "khoaXuat": "<PERSON><PERSON><PERSON>", "nhapMaQRCode": "Nhập mã QR Code", "maMau": "Mã mẫu", "video": "Video", "vuiLongThucHienThanhToanDichVu": "<PERSON><PERSON> lòng thực hiện thanh to<PERSON> dịch vụ <br/> tr<PERSON><PERSON><PERSON> khi checkin", "xinMoiNguoiBenhSttVao": "<PERSON>n mời ng<PERSON>ời bệnh stt {{ stt }} vào {{ ten }}", "xinMoiNguoiBenhTenVao": "<PERSON>n mời ng<PERSON> b<PERSON>nh {{ tenNb }} {{ tuoi2 }} vào {{ ten }}", "xacThucThongTinBangKhuonMat": "<PERSON><PERSON><PERSON> thực thông tin bằng khuôn mặt", "tuyenBoQuyenRiengTu": "<PERSON><PERSON><PERSON><PERSON> b<PERSON> quyền riêng tư", "noiDungQuyenRiengTu": {"tieuDe1": "Xin vui lòng để thẻ CCCD của quý khách vào vị trí quét. Thông tin trên CCCD sẽ được sử dụng trong quá trình nhận diện khuôn mặt.", "tieuDe2": "ISOFH cam kết và tôn trọng quyền riêng tư của mọi quý khách khi sử dụng thông tin khuôn mặt. Chúng tôi hiểu rằng quyền riêng tư là một giá trị quan trọng và cam kết bảo vệ thông tin cá nhân của quý khách một cách đáng tin cậy. Dưới đây là tuyên bố quyền riêng tư mà chúng tôi đưa ra:", "tieuDeChiTiet1": "Sự Đồng Ý R<PERSON>ng", "noiDungChiTiet1": "Mọi sử dụng thông tin khuôn mặt của quý khách đều phải được sự đồng ý rõ ràng từ phía quý khách trước. Chúng tôi cam kết không sử dụng hoặc chia sẻ thông tin này mà không có sự đồng ý từ quý khách.", "tieuDeChiTiet2": "<PERSON><PERSON>", "noiDungChiTiet2": "Mọi hoạt động liên quan đến sử dụng thông tin khuôn mặt của quý khách sẽ tuân thủ đầy đủ các quy định và luật lệ pháp luật về quyền riêng tư và bảo vệ dữ liệu cá nhân.", "tieuDeChiTiet3": "<PERSON><PERSON><PERSON>", "noiDungChiTiet3": "<PERSON><PERSON>g tôi cam kết triển khai các biện pháp bảo mật mạnh mẽ để đảm bảo thông tin khuôn mặt của NB được bảo vệ khỏi sự truy cập trái phép hoặc lạm dụng.", "tieuDeChiTiet4": "<PERSON><PERSON><PERSON>", "noiDungChiTiet4": "<PERSON>úng tôi sẽ chỉ sử dụng thông tin khuôn mặt của quý khách cho mục đích được đặt ra rõ ràng và được thông báo trước, nh<PERSON> xác thực người dùng hoặc cung cấp các dịch vụ liên quan.", "tieuDeChiTiet5": "<PERSON><PERSON><PERSON>n <PERSON>hu <PERSON> và Xóa Thông Tin", "noiDungChiTiet5": "<PERSON>uy<PERSON> khách có quyền thu hồi sự đồng ý sử dụng thông tin khuôn mặt của mình bất kỳ lúc nào, và chúng tôi cam kết xóa hoặc ngưng sử dụng thông tin đó theo yêu cầu của quý khách.", "noiDungCuoi": "Chúng tôi tin rằng việc bảo vệ quyền riêng tư của quý khách không chỉ là trách nhiệm pháp lý mà còn là nền tảng để xây dựng niềm tin và sự hài lòng từ phía người dùng. Quyền riêng tư là giá trị cốt lõi của chúng tôi và chúng tôi cam kết duy trì và nâng cao mức độ bảo vệ này trong mọi hoạt động của công ty."}, "toiDaDocVaDongYVoiCacDieuKhoan": "Tôi đã đọc và đồng ý với các điều khoản", "khongTheTimthayTheDuocDatOMayQuet": "(<PERSON><PERSON><PERSON><PERSON> tìm thấy thẻ được đặt ở máy quét)", "tiepTuc": "<PERSON><PERSON><PERSON><PERSON>", "nhanDienKhuonMat": "<PERSON><PERSON><PERSON><PERSON> di<PERSON>n khuôn mặt", "quayGoiSo": "<PERSON><PERSON><PERSON><PERSON><PERSON> số", "hienThiLenQMS": "<PERSON><PERSON><PERSON> thị lên <PERSON>", "tongSoDaLay": "Tổng số đã lấy", "sttKhongTonTaiTrongNgay": "<PERSON><PERSON> thứ tự không tồn tại trong ngày!", "sttDaTiepDonKhongTheTiepDonLaiTrongNgay": "STT đã tiếp đón, không thể tiếp đón lại trong ngày!", "taiISOFHCARE": "Tải ISOFHCARE", "datKhamTrucTuyen": "Đặt khám trực tuyến", "datLichXetNghiemCoVid": "Đặt lịch x<PERSON>t ng<PERSON>", "khamQuaVideoCall": "K<PERSON><PERSON><PERSON> qua video call", "dangThucHien": "ĐANG THỰC HIỆN", "dangThucHienEng": "IN PROGRESS", "vuiLongChonLoaiHienThi": "<PERSON>ui lòng chọn loại hiển thị!", "vuiLongChonToiDa8Phong": "<PERSON>ui lòng chọn tối đa {{soLuong}} phòng!", "vuiLongThietLapPhong": "Vui lòng thiết lập phòng!", "vuiLongThietLapQuay": "Vui lòng thiết lập quầy!", "chuThichTruongKieuGoiSo": "<PERSON><PERSON> thích trường <PERSON> số", "soThuTuBenhNhan": "<PERSON><PERSON> thứ tự bệnh nhân", "tenBenhNhan": "<PERSON><PERSON><PERSON> b<PERSON> nhân", "tuoiBenhNhan": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> nhân", "pppt": "PPPT", "phauThuatXong": "Phẫu thuật xong", "sttDangTiepNhan": "STT đang tiếp nhận", "soLanGoi1Lan": "Số lần gọi 1 lần", "soLanGoi": "Số lần g<PERSON>i", "nguoiBenhUuTien": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> <PERSON> tiên", "soNb1LanGoi": "Số NB 1 lần gọi", "soThuTuTiepTheo": "<PERSON><PERSON> thứ tự tiếp theo", "nguoiBenhThuong": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> th<PERSON>", "nguoiBenhChoKhamUuTien": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>nh chờ khám ưu tiên", "nguoiBenhChoKhamThuong": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> chờ khám thường", "nguoiBenhChoLayMauUuTien": "<PERSON><PERSON><PERSON><PERSON> bệnh chờ lấy mẫu ưu tiên", "nguoiBenhChoLayMauThuong": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>nh chờ lấy mẫu thường", "nguoiBenhChoThucHienUuTien": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> chờ thực hiện ưu tiên", "nguoiBenhChoThucHienThuong": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> chờ thực hiện thường", "banCoChacChanMuonGoiSTTTiepTheo": "Bạn có chắc chắn muốn gọi số thứ tự tiếp theo không?", "goiSTTTiepTheoThanhCong": "Gọi STT tiếp theo thành công!", "nguoiBenhChuanBi": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> ch<PERSON> bị", "daKhamDenSoThuTu": "<PERSON><PERSON> khám đến số thứ tự", "nguoiBenhCoSoThuTuThapHon": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> có số thứ tự thấp hơn số thứ tự {{stt}} đ<PERSON><PERSON><PERSON> quyền vào kh<PERSON>m", "soThuTu": "<PERSON><PERSON> thứ tự", "moiNguoiBenh": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>nh", "soThuTuTiepDon": "<PERSON><PERSON> thứ tự tiếp đón", "goiSo": "<PERSON><PERSON><PERSON> s<PERSON>", "choThanhToan": "Chờ thanh toán", "dangThanhToan": "<PERSON><PERSON>h toán", "daThanhToan": "<PERSON><PERSON> thanh toán", "quayPhatThuocBhyt": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON>t thuốc BHYT", "choPhatThuoc": "<PERSON><PERSON> ph<PERSON>t thu<PERSON>c", "dangPhatThuoc": "<PERSON><PERSON> p<PERSON> th<PERSON>c"}